# Advanced Markov AI with State Space and Attention Mechanisms
# A Sophisticated Approach to High-Quality Text Generation

"""
This implementation features an Advanced Markov AI system that combines:
1. Multi-level state space representation for long-term context
2. Custom attention mechanisms for dynamic context focus
3. Instruction tuning capabilities for task-specific optimization
4. Advanced sampling strategies for LLM-quality generation
5. Hierarchical memory systems for maintaining coherence

The system bridges traditional statistical models with modern attention mechanisms,
providing interpretability, semantic coherence, and instruction-following capabilities.
"""

# ============================================================================
# IMPORTS AND SETUP
# ============================================================================

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter, deque
import re
import pickle
import json
from typing import Dict, List, Tuple, Optional, Any, Union
import warnings
import math
from dataclasses import dataclass
from abc import ABC, abstractmethod
warnings.filterwarnings('ignore')

# For word embeddings and similarity
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer

# Try to import gensim for pre-trained embeddings (optional)
try:
    import gensim.downloader as api
    GENSIM_AVAILABLE = True
except ImportError:
    GENSIM_AVAILABLE = False
    print("⚠️ Gensim not available - will use contextual embeddings only")

# For knowledge base and RAG
from sklearn.feature_extraction.text import CountVectorizer

# For text processing
import nltk
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.corpus import stopwords
import string

# Download required NLTK data with error handling
def setup_nltk():
    """Setup NLTK data with proper error handling."""
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        print("📥 Downloading NLTK punkt tokenizer...")
        try:
            nltk.download('punkt', quiet=True)
        except:
            print("⚠️ Could not download punkt tokenizer, using basic tokenization")

    try:
        nltk.data.find('corpora/stopwords')
    except LookupError:
        print("📥 Downloading NLTK stopwords...")
        try:
            nltk.download('stopwords', quiet=True)
        except:
            print("⚠️ Could not download stopwords, continuing without them")

# Setup NLTK
setup_nltk()

print("🚀 Advanced Markov AI Implementation Starting...")
print("📊 All required libraries imported successfully!")
# ============================================================================
# DISCOURSE PLANNING & GOAL MAINTENANCE MODULE
# ============================================================================

class DiscoursePlanner:
    """
    DiscoursePlanner breaks down a prompt into a structured outline (sections/goals),
    tracks progress, and maintains awareness of what needs to be accomplished.
    """

    def __init__(self):
        self.plan = []
        self.current_section = 0
        self.completed_sections = set()

    def create_plan(self, prompt: str) -> list:
        """
        Given a prompt, generate a logical outline for the response.
        Returns a list of sections/goals.
        """
        # Simple heuristic-based planning for demonstration
        prompt_lower = prompt.lower()
        plan = []
        if "explain" in prompt_lower:
            plan = ["Introduction", "Core Concepts", "Applications", "Conclusion"]
        elif "compare" in prompt_lower or "difference" in prompt_lower:
            plan = ["Introduction", "Definition of Terms", "Key Differences", "Summary"]
        elif "how" in prompt_lower:
            plan = ["Introduction", "Step-by-Step Process", "Examples", "Summary"]
        else:
            plan = ["Introduction", "Main Content", "Conclusion"]
        self.plan = plan
        self.current_section = 0
        self.completed_sections = set()
        return plan

    def next_section(self) -> str:
        """
        Move to the next section in the plan.
        """
        if self.current_section < len(self.plan):
            section = self.plan[self.current_section]
            self.current_section += 1
            return section
        return None

    def mark_section_complete(self, section: str):
        self.completed_sections.add(section)

    def is_complete(self) -> bool:
        return len(self.completed_sections) == len(self.plan)

    def get_progress(self) -> dict:
        return {
            "plan": self.plan,
            "completed": list(self.completed_sections),
            "current_section": self.current_section
        }

# ============================================================================
# COGNITIVE-CAUSAL REASONING CORE (CCRC) - DATA STRUCTURES
# ============================================================================

@dataclass
class CognitiveConceptNode:
    """
    Represents a concept with its properties and states within the reasoning graph.
    This is a richer representation than a simple keyword.
    """
    concept_text: str
    embedding: np.ndarray
    frequency: int
    importance_score: float
    properties: Dict[str, Any] = None  # e.g., {'type': 'algorithm', 'function': 'prediction'}
    states: List[str] = None           # e.g., ['training', 'inference']
    evidence_sentences: List[str] = None

    def __post_init__(self):
        if self.properties is None: self.properties = {}
        if self.states is None: self.states = []
        if self.evidence_sentences is None: self.evidence_sentences = []

@dataclass
class RelationEdge:
    """Represents a directed, typed relationship between two concepts."""
    source_concept: str
    target_concept: str
    relation_type: str  # 'causes', 'enables', 'is_a', 'has_property', 'uses'
    confidence: float
    evidence: List[str]

@dataclass
class ReasoningPrinciple:
    """
    Represents an abstracted rule or pattern induced from the knowledge graph.
    This is the foundation for deep reasoning and synthesis.
    """
    name: str
    # e.g., ('?subject:AI_System', 'uses', '?object:Technique')
    abstract_structure: Tuple[str, str, str] 
    instantiations: List[RelationEdge]
    confidence: float

class ReasoningGraph:
    """
    The core knowledge structure, a multi-layered graph of concepts, relations, and principles.
    This is the AI's "brain" or "mental model" of the domain.
    """
    def __init__(self):
        self.concepts: Dict[str, CognitiveConceptNode] = {}
        self.relations: List[RelationEdge] = []
        self.principles: List[ReasoningPrinciple] = []
        
        # For fast lookups
        self.relations_from: Dict[str, List[RelationEdge]] = defaultdict(list)
        self.relations_to: Dict[str, List[RelationEdge]] = defaultdict(list)
        self.concept_embeddings_matrix = None
        self.concept_text_list = []

    def add_concept(self, concept: CognitiveConceptNode):
        self.concepts[concept.concept_text] = concept

    def add_relation(self, relation: RelationEdge):
        if relation.source_concept in self.concepts and relation.target_concept in self.concepts:
            self.relations.append(relation)
            self.relations_from[relation.source_concept].append(relation)
            self.relations_to[relation.target_concept].append(relation)

    def add_principle(self, principle: ReasoningPrinciple):
        self.principles.append(principle)

    def finalize(self):
        """Builds indices for fast retrieval after the graph is populated."""
        if not self.concepts: return
        self.concept_text_list = list(self.concepts.keys())
        embeddings = [self.concepts[text].embedding for text in self.concept_text_list]
        self.concept_embeddings_matrix = np.array(embeddings)
        
    def find_similar_concepts(self, query_embedding: np.ndarray, k: int = 5) -> List[CognitiveConceptNode]:
        """Find concepts most similar to the query embedding."""
        if self.concept_embeddings_matrix is None or len(self.concept_embeddings_matrix) == 0:
            return []
            
        sims = cosine_similarity([query_embedding], self.concept_embeddings_matrix)[0]
        top_k_indices = np.argsort(sims)[-k:][::-1]
        
        return [self.concepts[self.concept_text_list[i]] for i in top_k_indices]

    def get_related_concepts(self, concept_text: str) -> List[Tuple[RelationEdge, CognitiveConceptNode]]:
        """Get all concepts directly related to the given concept."""
        related = []
        if concept_text in self.relations_from:
            for rel in self.relations_from[concept_text]:
                related.append((rel, self.concepts.get(rel.target_concept)))
        if concept_text in self.relations_to:
            for rel in self.relations_to[concept_text]:
                related.append((rel, self.concepts.get(rel.source_concept)))
        return [item for item in related if item[1] is not None]

# ============================================================================
# DATA STRUCTURES FOR STATE SPACE SYSTEM
# ============================================================================

@dataclass
class AttentionHead:
    """Single attention head for multi-head attention mechanism."""
    query_weights: np.ndarray
    key_weights: np.ndarray
    value_weights: np.ndarray
    output_weights: np.ndarray
    head_dim: int

@dataclass
class StateSnapshot:
    """Represents a snapshot of the generation state at a specific point."""
    tokens: List[str]
    embeddings: List[np.ndarray]
    attention_weights: np.ndarray
    context_vector: np.ndarray
    timestamp: int
    importance_score: float

@dataclass
class ContextualMemory:
    """Enhanced memory structure with richer context representation."""
    tokens: List[str]
    semantic_summary: np.ndarray  # Attention-weighted semantic representation
    syntactic_pattern: np.ndarray  # N-gram pattern encoding
    importance_score: float
    recency_weight: float
    topic_vector: np.ndarray  # Topic/domain representation
    timestamp: int

@dataclass
class InstructionExample:
    """Training example for instruction tuning."""
    instruction: str
    input_text: str
    expected_output: str
    quality_score: float

class MemoryBank:
    """Hierarchical memory system for maintaining long-term context."""
    
    def __init__(self, capacity: int = 1000, embedding_dim: int = 100):
        self.capacity = capacity
        self.embedding_dim = embedding_dim
        self.short_term = deque(maxlen=50)  # Recent context
        self.medium_term = deque(maxlen=200)  # Important patterns
        self.long_term = []  # Persistent memories
        self.importance_threshold = 0.7
        
    def add_memory(self, snapshot: StateSnapshot):
        """Add a new memory snapshot with importance-based routing."""
        self.short_term.append(snapshot)
        
        if snapshot.importance_score > self.importance_threshold:
            self.medium_term.append(snapshot)
            
        if snapshot.importance_score > 0.9:
            self.long_term.append(snapshot)
            # Keep only most important long-term memories
            if len(self.long_term) > self.capacity // 10:
                self.long_term.sort(key=lambda x: x.importance_score, reverse=True)
                self.long_term = self.long_term[:self.capacity // 10]
    
    def retrieve_relevant_memories(self, query_vector: np.ndarray, k: int = 10) -> List[StateSnapshot]:
        """Retrieve most relevant memories based on query vector."""
        all_memories = list(self.short_term) + list(self.medium_term) + self.long_term
        
        if not all_memories:
            return []
        
        # Calculate similarities
        similarities = []
        for memory in all_memories:
            sim = cosine_similarity([query_vector], [memory.context_vector])[0][0]
            similarities.append((sim * memory.importance_score, memory))
        
        # Sort by weighted similarity and return top-k
        similarities.sort(key=lambda x: x[0], reverse=True)
        return [mem for _, mem in similarities[:k]]

# ============================================================================
# ATTENTION MECHANISMS
# ============================================================================

class MultiHeadAttention:
    """Multi-head attention mechanism for context focusing."""
    
    def __init__(self, embedding_dim: int, num_heads: int = 8, dropout: float = 0.1):
        self.embedding_dim = embedding_dim
        self.num_heads = num_heads
        self.head_dim = embedding_dim // num_heads
        self.dropout = dropout
        
        # Initialize attention heads
        self.heads = []
        for _ in range(num_heads):
            head = AttentionHead(
                query_weights=np.random.normal(0, 0.02, (embedding_dim, self.head_dim)),
                key_weights=np.random.normal(0, 0.02, (embedding_dim, self.head_dim)),
                value_weights=np.random.normal(0, 0.02, (embedding_dim, self.head_dim)),
                output_weights=np.random.normal(0, 0.02, (self.head_dim, embedding_dim)),
                head_dim=self.head_dim
            )
            self.heads.append(head)
        
        # Final output projection
        self.output_projection = np.random.normal(0, 0.02, (embedding_dim, embedding_dim))
    
    def attention(self, query: np.ndarray, keys: np.ndarray, values: np.ndarray, 
                  head: AttentionHead, mask: Optional[np.ndarray] = None) -> Tuple[np.ndarray, np.ndarray]:
        """Compute attention for a single head."""
        # Project to head dimension
        q = np.dot(query, head.query_weights)  # (seq_len, head_dim)
        k = np.dot(keys, head.key_weights)     # (seq_len, head_dim)
        v = np.dot(values, head.value_weights) # (seq_len, head_dim)
        
        # Compute attention scores
        scores = np.dot(q, k.T) / math.sqrt(self.head_dim)  # (seq_len, seq_len)
        
        # Apply mask if provided
        if mask is not None:
            scores = np.where(mask, scores, -np.inf)
        
        # Softmax to get attention weights
        attention_weights = self._softmax(scores)
        
        # Apply dropout (simplified)
        if self.dropout > 0:
            attention_weights = self._apply_dropout(attention_weights, self.dropout)
        
        # Apply attention to values
        output = np.dot(attention_weights, v)  # (seq_len, head_dim)
        
        return output, attention_weights
    
    def forward(self, embeddings: np.ndarray, mask: Optional[np.ndarray] = None) -> Tuple[np.ndarray, np.ndarray]:
        """Forward pass through multi-head attention."""
        seq_len, embed_dim = embeddings.shape
        
        # Compute attention for each head
        head_outputs = []
        all_attention_weights = []
        
        for head in self.heads:
            output, weights = self.attention(embeddings, embeddings, embeddings, head, mask)
            head_outputs.append(output)
            all_attention_weights.append(weights)
        
        # Concatenate head outputs
        concatenated = np.concatenate(head_outputs, axis=-1)  # (seq_len, embedding_dim)
        
        # Final projection
        output = np.dot(concatenated, self.output_projection)
        
        # Residual connection and layer norm (simplified)
        output = output + embeddings
        output = self._layer_norm(output)
        
        # Average attention weights across heads
        avg_attention = np.mean(all_attention_weights, axis=0)
        
        return output, avg_attention
    
    def _softmax(self, x: np.ndarray) -> np.ndarray:
        """Numerically stable softmax."""
        x_max = np.max(x, axis=-1, keepdims=True)
        exp_x = np.exp(x - x_max)
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)
    
    def _apply_dropout(self, x: np.ndarray, dropout_rate: float) -> np.ndarray:
        """Apply dropout (simplified implementation)."""
        mask = np.random.random(x.shape) > dropout_rate
        return x * mask / (1 - dropout_rate)
    
    def _layer_norm(self, x: np.ndarray, eps: float = 1e-6) -> np.ndarray:
        """Layer normalization."""
        mean = np.mean(x, axis=-1, keepdims=True)
        std = np.std(x, axis=-1, keepdims=True)
        return (x - mean) / (std + eps)

# ============================================================================
# CONTEXTUAL MEMORY ATTENTION SYSTEM
# ============================================================================

class ContextualMemoryAttention:
    """Novel attention mechanism designed specifically for Markov AI."""
    
    def __init__(self, embedding_dim: int, local_window: int = 64, global_memories: int = 12):
        self.embedding_dim = embedding_dim
        self.local_window = local_window
        self.global_memories = global_memories
        
        # Learnable parameters for different attention types
        self.local_attention_weights = np.random.normal(0, 0.02, (embedding_dim, embedding_dim))
        self.global_attention_weights = np.random.normal(0, 0.02, (embedding_dim, embedding_dim))
        self.fusion_weights = np.random.normal(0, 0.02, (embedding_dim * 2, embedding_dim))
        
        # Memory bank with enhanced structure
        self.contextual_memories = deque(maxlen=1000)
        self.topic_clusters = {}  # Topic-based memory organization
        
    def create_contextual_memory(self, tokens: List[str], embeddings: List[np.ndarray],
                               markov_state: tuple, generation_step: int) -> ContextualMemory:
        """Create a rich contextual memory from current state."""
        if not embeddings:
            return None
            
        # 1. Semantic Summary: Attention-weighted combination
        attention_weights = self._compute_semantic_attention(embeddings)
        semantic_summary = np.sum([w * emb for w, emb in zip(attention_weights, embeddings)], axis=0)
        
        # 2. Syntactic Pattern: Encode n-gram structure
        syntactic_pattern = self._encode_syntactic_pattern(tokens, markov_state)
        
        # 3. Topic Vector: Domain/topic representation
        topic_vector = self._compute_topic_vector(tokens, embeddings)
        
        # 4. Importance Score: Multi-factor importance (now includes diversity)
        importance = self._compute_contextual_importance(
            tokens, embeddings, semantic_summary, syntactic_pattern
        )
        
        # 5. Recency Weight: Exponential decay
        recency_weight = np.exp(-0.1 * generation_step)
        
        return ContextualMemory(
            tokens=tokens.copy(),
            semantic_summary=semantic_summary,
            syntactic_pattern=syntactic_pattern,
            importance_score=importance,
            recency_weight=recency_weight,
            topic_vector=topic_vector,
            timestamp=generation_step
        )
    
    def detect_repetition_patterns(self, recent_memories: List[ContextualMemory],
                                 current_tokens: List[str]) -> float:
        """Detect repetition patterns in recent context."""
        if len(recent_memories) < 2:
            return 0.0
        
        repetition_score = 0.0
        current_text = ' '.join(current_tokens).lower()
        
        # Check for exact phrase repetition
        for memory in recent_memories[-10:]:  # Last 10 memories
            memory_text = ' '.join(memory.tokens).lower()
            
            # Find common subsequences
            words_current = current_text.split()
            words_memory = memory_text.split()
            
            # Check for repeated 3-grams or longer
            for i in range(len(words_current) - 2):
                trigram = ' '.join(words_current[i:i+3])
                if trigram in memory_text and len(trigram.split()) >= 3:
                    repetition_score += 0.3  # Heavy penalty for 3+ word repetition
            
            # Check for semantic similarity (potential paraphrasing loops)
            if len(memory.semantic_summary) > 0:
                current_embedding = np.mean([self._get_token_embedding(token) for token in current_tokens], axis=0)
                semantic_sim = np.dot(current_embedding, memory.semantic_summary) / (
                    np.linalg.norm(current_embedding) * np.linalg.norm(memory.semantic_summary) + 1e-8
                )
                if semantic_sim > 0.85:  # Very high semantic similarity
                    repetition_score += 0.2
        
        return min(repetition_score, 1.0)
    
    def _get_token_embedding(self, token: str) -> np.ndarray:
        """Get embedding for a token, with fallback."""
        # This will be set by the parent class
        if hasattr(self, '_parent_embeddings') and token in self._parent_embeddings:
            return self._parent_embeddings[token]
        return np.random.normal(0, 0.1, self.embedding_dim)
    
    def _compute_semantic_attention(self, embeddings: List[np.ndarray]) -> List[float]:
        """Compute attention weights for semantic summarization."""
        if len(embeddings) <= 1:
            return [1.0] * len(embeddings)
            
        # Self-attention over embeddings
        embeddings_matrix = np.array(embeddings)
        scores = np.dot(embeddings_matrix, embeddings_matrix.T)
        
        # Apply softmax to get attention weights
        attention_weights = []
        for i in range(len(embeddings)):
            weights = np.exp(scores[i]) / np.sum(np.exp(scores[i]))
            attention_weights.append(np.mean(weights))  # Average attention received
            
        return attention_weights
    
    def _encode_syntactic_pattern(self, tokens: List[str], markov_state: tuple) -> np.ndarray:
        """Encode syntactic/structural patterns."""
        pattern = np.zeros(self.embedding_dim // 4)  # Use quarter of embedding dim
        
        # Encode n-gram structure
        if markov_state:
            state_hash = hash(markov_state) % len(pattern)
            pattern[state_hash] = 1.0
            
        # Encode POS-like patterns (simplified)
        for i, token in enumerate(tokens[-4:]):  # Last 4 tokens
            if i < len(pattern) // 4:
                # Simple morphological features
                if token.endswith('ing'):
                    pattern[i * 4] = 0.8
                elif token.endswith('ed'):
                    pattern[i * 4 + 1] = 0.8
                elif token.endswith('ly'):
                    pattern[i * 4 + 2] = 0.8
                else:
                    pattern[i * 4 + 3] = 0.8
                    
        return pattern
    
    def _compute_topic_vector(self, tokens: List[str], embeddings: List[np.ndarray]) -> np.ndarray:
        """Compute topic/domain representation."""
        topic_vector = np.zeros(self.embedding_dim // 4)
        
        # Domain keywords (simplified topic modeling)
        domains = {
            'technical': ['algorithm', 'data', 'system', 'process', 'method'],
            'ai_ml': ['learning', 'neural', 'model', 'intelligence', 'training'],
            'scientific': ['research', 'analysis', 'study', 'theory', 'experiment'],
            'business': ['market', 'customer', 'product', 'service', 'business']
        }
        
        text = ' '.join(tokens).lower()
        for i, (domain, keywords) in enumerate(domains.items()):
            if i < len(topic_vector):
                score = sum(1 for keyword in keywords if keyword in text)
                topic_vector[i] = score / len(keywords)
                
        return topic_vector
    
    def _compute_contextual_importance(self, tokens: List[str], embeddings: List[np.ndarray],
                                     semantic_summary: np.ndarray, syntactic_pattern: np.ndarray) -> float:
        """Compute multi-factor importance score."""
        factors = []
        
        # 1. Semantic diversity
        if len(embeddings) > 1:
            similarities = []
            for i in range(len(embeddings)):
                for j in range(i + 1, len(embeddings)):
                    sim = np.dot(embeddings[i], embeddings[j]) / (
                        np.linalg.norm(embeddings[i]) * np.linalg.norm(embeddings[j]) + 1e-8
                    )
                    similarities.append(sim)
            diversity = 1.0 - np.mean(similarities) if similarities else 0.5
            factors.append(diversity)
        
        # 2. Syntactic complexity
        complexity = np.sum(syntactic_pattern > 0) / len(syntactic_pattern)
        factors.append(complexity)
        
        # 3. Semantic richness (norm of summary)
        richness = np.linalg.norm(semantic_summary) / self.embedding_dim
        factors.append(richness)
        
        # 4. Token rarity (simplified)
        rare_bonus = sum(1 for token in tokens if len(token) > 6) / max(len(tokens), 1)
        factors.append(rare_bonus)
        
        return np.mean(factors)
    
    def retrieve_relevant_memories(self, current_context: np.ndarray,
                                 current_topic: np.ndarray, current_tokens: List[str] = None) -> List[ContextualMemory]:
        """Retrieve most relevant memories using multi-factor similarity with diversity filtering."""
        if not self.contextual_memories:
            return []
        
        # Detect repetition patterns if current tokens provided
        repetition_penalty = 0.0
        if current_tokens:
            repetition_penalty = self.detect_repetition_patterns(
                list(self.contextual_memories)[-20:], current_tokens
            )
            
        scored_memories = []
        for memory in self.contextual_memories:
            # Multi-factor similarity
            semantic_sim = np.dot(current_context, memory.semantic_summary) / (
                np.linalg.norm(current_context) * np.linalg.norm(memory.semantic_summary) + 1e-8
            )
            
            topic_sim = np.dot(current_topic, memory.topic_vector) / (
                np.linalg.norm(current_topic) * np.linalg.norm(memory.topic_vector) + 1e-8
            )
            
            # Anti-repetition penalty: reduce score for very recent similar memories
            anti_repetition_factor = 1.0
            if current_tokens and repetition_penalty > 0.3:
                memory_text = ' '.join(memory.tokens).lower()
                current_text = ' '.join(current_tokens).lower()
                
                # Penalize memories with high text overlap
                overlap_words = set(memory_text.split()) & set(current_text.split())
                if len(overlap_words) > len(current_text.split()) * 0.6:  # >60% overlap
                    anti_repetition_factor = 0.3  # Heavy penalty
                elif len(overlap_words) > len(current_text.split()) * 0.4:  # >40% overlap
                    anti_repetition_factor = 0.6  # Moderate penalty
            
            # Combined score with importance, recency, and anti-repetition
            combined_score = (
                0.4 * semantic_sim +
                0.3 * topic_sim +
                0.2 * memory.importance_score +
                0.1 * memory.recency_weight
            ) * anti_repetition_factor
            
            scored_memories.append((combined_score, memory))
        
        # Sort by score
        scored_memories.sort(key=lambda x: x[0], reverse=True)
        
        # Diversity filtering: ensure we don't select too many similar memories
        selected_memories = []
        for score, memory in scored_memories:
            if len(selected_memories) >= self.global_memories:
                break
                
            # Check diversity against already selected memories
            is_diverse = True
            for selected_memory in selected_memories:
                similarity = np.dot(memory.semantic_summary, selected_memory.semantic_summary) / (
                    np.linalg.norm(memory.semantic_summary) * np.linalg.norm(selected_memory.semantic_summary) + 1e-8
                )
                if similarity > 0.9:  # Too similar to already selected memory
                    is_diverse = False
                    break
            
            if is_diverse:
                selected_memories.append(memory)
        
        return selected_memories
    
    def compute_contextual_attention(self, local_embeddings: np.ndarray,
                                   relevant_memories: List[ContextualMemory],
                                   markov_probs: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """Compute final attention-enhanced context."""
        batch_size = local_embeddings.shape[0]
        
        # 1. Local attention (high-resolution)
        local_attended = self._apply_local_attention(local_embeddings)
        
        # 2. Global memory attention (low-resolution)
        if relevant_memories:
            memory_vectors = np.array([mem.semantic_summary for mem in relevant_memories])
            global_attended = self._apply_global_attention(local_attended, memory_vectors)
        else:
            global_attended = local_attended
        
        # 3. Fusion of local and global
        fused_context = self._fuse_contexts(local_attended, global_attended)
        
        # 4. Integration with Markov probabilities
        enhanced_context = self._integrate_with_markov(fused_context, markov_probs)
        
        attention_info = {
            'local_attention_strength': np.mean(np.abs(local_attended - local_embeddings)),
            'global_memories_used': len(relevant_memories),
            'fusion_strength': np.linalg.norm(fused_context),
            'markov_integration': np.mean(markov_probs)
        }
        
        return enhanced_context, attention_info
    
    def _apply_local_attention(self, embeddings: np.ndarray) -> np.ndarray:
        """Apply attention within local window."""
        # Self-attention within local context
        attended = np.dot(embeddings, self.local_attention_weights)
        return attended + embeddings  # Residual connection
    
    def _apply_global_attention(self, local_context: np.ndarray,
                              memory_vectors: np.ndarray) -> np.ndarray:
        """Apply attention to global memories."""
        # Cross-attention between local context and memories
        attention_scores = np.dot(local_context, memory_vectors.T)
        attention_weights = self._softmax(attention_scores)
        
        # Weighted combination of memories
        global_context = np.dot(attention_weights, memory_vectors)
        return global_context
    
    def _fuse_contexts(self, local: np.ndarray, global_ctx: np.ndarray) -> np.ndarray:
        """Fuse local and global contexts."""
        if global_ctx.shape != local.shape:
            # Handle shape mismatch
            if len(global_ctx.shape) == 1:
                # Expand global to match local batch dimension
                global_ctx = np.tile(global_ctx, (local.shape[0], 1))
            else:
                # Trim global to match local batch dimension
                global_ctx = global_ctx[:local.shape[0]]
        
        # Ensure fusion weights match the concatenated dimension
        expected_concat_dim = local.shape[-1] + global_ctx.shape[-1]
        if self.fusion_weights.shape[0] != expected_concat_dim:
            # Reinitialize fusion weights with correct dimensions
            self.fusion_weights = np.random.normal(0, 0.02, (expected_concat_dim, self.embedding_dim))
        
        # Concatenate and project
        combined = np.concatenate([local, global_ctx], axis=-1)
        fused = np.dot(combined, self.fusion_weights)
        return fused
    
    def _integrate_with_markov(self, context: np.ndarray, markov_probs: np.ndarray) -> np.ndarray:
        """Integrate attention context with Markov probabilities."""
        # Use context to modulate Markov probabilities
        context_influence = np.mean(context, axis=0) if len(context.shape) > 1 else context
        
        # Simple integration (can be made more sophisticated)
        integration_factor = 1.0 + 0.1 * np.tanh(np.linalg.norm(context_influence))
        return context * integration_factor
    
    def _softmax(self, x: np.ndarray) -> np.ndarray:
        """Numerically stable softmax."""
        exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)

# ============================================================================
# INSTRUCTION TUNING SYSTEM
# ============================================================================

class InstructionTuner:
    """System for instruction tuning the Markov AI model."""
    
    def __init__(self, model_ref):
        self.model = model_ref
        self.training_examples = []
        self.quality_metrics = {
            'coherence': [],
            'relevance': [],
            'instruction_following': [],
            'fluency': []
        }
        
    def add_training_example(self, instruction: str, input_text: str, 
                           expected_output: str, quality_score: float = 1.0):
        """Add a training example for instruction tuning."""
        example = InstructionExample(
            instruction=instruction,
            input_text=input_text,
            expected_output=expected_output,
            quality_score=quality_score
        )
        self.training_examples.append(example)
    
    def evaluate_generation_quality(self, generated_text: str, expected_text: str) -> Dict[str, float]:
        """Evaluate the quality of generated text against expected output."""
        # Simplified quality metrics
        generated_tokens = self.model.preprocess_text(generated_text)
        expected_tokens = self.model.preprocess_text(expected_text)
        
        # Token overlap (simplified BLEU-like metric)
        common_tokens = set(generated_tokens) & set(expected_tokens)
        token_overlap = len(common_tokens) / max(len(set(generated_tokens)), 1)
        
        # Length similarity
        length_ratio = min(len(generated_tokens), len(expected_tokens)) / max(len(generated_tokens), len(expected_tokens))
        
        # Semantic similarity using embeddings
        if generated_tokens and expected_tokens:
            gen_embedding = self._get_text_embedding(generated_tokens)
            exp_embedding = self._get_text_embedding(expected_tokens)
            semantic_sim = cosine_similarity([gen_embedding], [exp_embedding])[0][0]
        else:
            semantic_sim = 0.0
        
        return {
            'token_overlap': token_overlap,
            'length_similarity': length_ratio,
            'semantic_similarity': max(0, semantic_sim),
            'overall_quality': (token_overlap + length_ratio + max(0, semantic_sim)) / 3
        }
    
    def _get_text_embedding(self, tokens: List[str]) -> np.ndarray:
        """Get embedding for a list of tokens."""
        embeddings = []
        for token in tokens: # pragma: no cover
            if token in self.model.word_embeddings:
                embeddings.append(self.model.word_embeddings[token])
        
        if embeddings:
            return np.mean(embeddings, axis=0)
        else:
            return np.zeros(self.model.embedding_dim)
    
    def optimize_parameters(self, learning_rate: float = 0.01, epochs: int = 10):
        """Optimize model parameters based on instruction tuning examples."""
        print(f"🎯 Starting instruction tuning with {len(self.training_examples)} examples...")
        
        for epoch in range(epochs):
            total_loss = 0
            for example in self.training_examples:
                # Generate response to instruction
                prompt = f"{example.instruction}\n{example.input_text}"
                generated, _ = self.model.generate_text_advanced(prompt, length=50, temperature=0.7)
                
                # Evaluate quality
                quality_metrics = self.evaluate_generation_quality(generated, example.expected_output)
                loss = 1.0 - quality_metrics['overall_quality']
                total_loss += loss
                self._adjust_parameters(quality_metrics, learning_rate)

    def _adjust_parameters(self, quality_metrics: Dict[str, float], learning_rate: float):
        """Adjust model parameters based on quality feedback."""
        # Simplified parameter adjustment
        if quality_metrics['semantic_similarity'] < 0.3:
            self.model.alpha = min(0.9, self.model.alpha + learning_rate * 0.1)
        
        if quality_metrics['token_overlap'] < 0.2:
            self.model.beta = min(0.6, self.model.beta + learning_rate * 0.05)
        
        if quality_metrics['length_similarity'] < 0.5:
            self.model.gamma = max(0.1, self.model.gamma - learning_rate * 0.02)

# ============================================================================
# COGNITIVE-CAUSAL REASONING CORE (CCRC) IMPLEMENTATION
# ============================================================================

class CognitiveCausalReasoningCore:
    """
    The CCRC transforms flat text into a structured, multi-layered reasoning graph.
    It is the core engine that enables deep reasoning and subject matter expertise.
    """
    
    def __init__(self, embedding_dim: int = 128):
        self.embedding_dim = embedding_dim
        self.tfidf_vectorizer = TfidfVectorizer(max_features=1000, ngram_range=(1, 3))
        
        # Mapping from verbs/phrases to standardized relation types
        self.relation_map = {
            'is a': 'is_a', 'is a type of': 'is_a', 'are': 'is_a',
            'includes': 'has_part', 'contains': 'has_part', 'consists of': 'has_part',
            'causes': 'causes', 'leads to': 'causes', 'results in': 'causes',
            'enables': 'enables', 'allows': 'enables',
            'uses': 'uses', 'employs': 'uses', 'utilizes': 'uses',
            'prevents': 'prevents', 'inhibits': 'prevents',
            'represents': 'is_a', 'is': 'is_a'
        }
    
    # ========================================================================
    # PHASE 1: BUILDING THE REASONING GRAPH (Build-Time Process)
    # ========================================================================
    
    def build_reasoning_graph(self, corpus: str, knowledge_base: List[str], 
                               word_embeddings: Dict[str, np.ndarray]) -> ReasoningGraph:
        """
        Phase 1: Build the complete reasoning graph from corpus and knowledge base.
        This is the core transformation that builds the AI's mental model.
        """
        print("🧠 Phase 1: Building the Cognitive-Causal Reasoning Graph...")
        print("=" * 60)
        
        graph = ReasoningGraph()
        
        # Combine all texts for analysis
        all_texts = [corpus] + knowledge_base
        all_text = ' '.join(all_texts)
        
        # Step 1: Extract Cognitive Concepts
        print("🔍 Step 1: Extracting Cognitive Concepts...")
        concepts = self._extract_cognitive_concepts(all_text, word_embeddings)
        for concept in concepts:
            graph.add_concept(concept)
        print(f"   ✅ Extracted {len(concepts)} cognitive concepts")
        
        # Step 2: Infer Causal and Relational Links
        print("🔗 Step 2: Inferring Relational Links...")
        relations = self._infer_relations(all_text, graph.concepts)
        for relation in relations:
            graph.add_relation(relation)
        print(f"   ✅ Inferred {len(relations)} relational links")
        
        # Step 3: Induce Reasoning Principles (The Abstraction Leap)
        print("💡 Step 3: Inducing Reasoning Principles...")
        principles = self._induce_principles(graph)
        for principle in principles:
            graph.add_principle(principle)
        print(f"   ✅ Induced {len(principles)} reasoning principles")
        
        # Finalize graph for efficient querying
        graph.finalize()
        
        print(f"🧠 Reasoning Graph Complete!")
        print(f"   • Concepts: {len(graph.concepts)}")
        print(f"   • Relations: {len(graph.relations)}")
        print(f"   • Principles: {len(graph.principles)}")
        
        return graph
    
    def _extract_cognitive_concepts(self, text: str, word_embeddings: Dict[str, np.ndarray]) -> List[CognitiveConceptNode]:
        """Extract core concepts using TF-IDF and basic syntactic filtering."""
        # Tokenize into sentences
        try:
            sentences = sent_tokenize(text)
        except:
            sentences = text.split('.')
        
        # Extract noun phrases and important terms
        concept_candidates = set()
        
        # Use TF-IDF to find important terms
        try:
            tfidf_matrix = self.tfidf_vectorizer.fit_transform([text])
            feature_names = self.tfidf_vectorizer.get_feature_names_out()
            tfidf_scores = tfidf_matrix.toarray()[0]
            
            # Get top scoring terms
            top_indices = np.argsort(tfidf_scores)[-50:]  # Top 50 terms
            for idx in top_indices:
                if tfidf_scores[idx] > 0:
                    concept_candidates.add(feature_names[idx])
        except: # pragma: no cover
            # Fallback: extract multi-word technical terms
            import re
            technical_terms = re.findall(r'\b[a-z]+(?:\s+[a-z]+){1,2}\b', text.lower())
            concept_candidates.update(technical_terms[:50])
        
        # Add a cleanup step for concept candidates
        try:
            stop_words = set(stopwords.words('english'))
        except: # pragma: no cover
            stop_words = set(['the', 'a', 'an', 'in', 'on', 'of', 'for', 'to', 'is', 'are', 'was', 'were'])

        cleaned_candidates = set()
        for candidate in concept_candidates: # pragma: no cover
            words = candidate.strip().split()
            if not words:
                continue
            # Filter out candidates that start/end with a stopword or are too long
            if words[0] in stop_words or words[-1] in stop_words:
                continue
            if len(words) > 5: # Avoid overly long, likely incorrect phrases
                continue
            # Filter out candidates that start with a verb (e.g., "encompasses machine learning")
            try:
                pos_tags = nltk.pos_tag([words[0]])
                if pos_tags and pos_tags[0][1].startswith("VB"):
                    continue
            except Exception:
                pass
            # Manual fallback for common verbs missed by POS tagger
            common_start_verbs = {"encompasses", "includes", "contains", "uses", "performs", "measures", "enables", "prevents", "causes", "leads", "results", "makes", "does", "is", "are", "was", "were"}
            if words[0].lower() in common_start_verbs:
                continue
            cleaned_candidates.add(candidate)

        # Build concept nodes
        concept_nodes = []
        for candidate in cleaned_candidates:
            if len(candidate.split()) >= 1:  # At least one word
                # Find sentences containing this concept
                concept_sentences = [s for s in sentences if candidate.lower() in s.lower()]
                if concept_sentences:
                    # Create embedding for concept
                    concept_embedding = self._get_text_embedding(candidate.split(), word_embeddings)
                    
                    # Calculate importance score
                    importance = len(concept_sentences) / len(sentences)
                    
                    concept_node = CognitiveConceptNode(
                        concept_text=candidate,
                        embedding=concept_embedding,
                        frequency=len(concept_sentences),
                        importance_score=importance,
                        evidence_sentences=concept_sentences[:5],
                        properties={}, states=[] # Properties/states would be populated by a deeper NLP pipeline
                    )
                    concept_nodes.append(concept_node)
        
        return concept_nodes
    
    def _get_text_embedding(self, tokens: List[str], embeddings_dict: Optional[Dict[str, np.ndarray]] = None) -> np.ndarray:
        """Get embedding for a list of tokens."""
        embeddings = []
        for token in tokens: # pragma: no cover
            if token in embeddings_dict:
                embeddings.append(embeddings_dict[token])
        
        if embeddings:
            return np.mean(embeddings, axis=0)
        else:
            # Fallback: create a hash-based embedding
            return np.random.normal(0, 0.1, self.embedding_dim)
    
    def _infer_relations(self, text: str, concepts: Dict[str, CognitiveConceptNode]) -> List[RelationEdge]:
        """Infer relations between concepts using SVO extraction and pattern matching."""
        relations = []
        try:
            sentences = sent_tokenize(text)
        except:
            sentences = text.split('.')

        for sentence in sentences:
            # Simplified SVO (Subject-Verb-Object) logic
            for verb_phrase, rel_type in self.relation_map.items():
                pattern = rf'(.*?)\s+{re.escape(verb_phrase)}\s+(.*)'
                match = re.search(pattern, sentence, re.IGNORECASE)
                if match:
                    subject_text = match.group(1).strip()
                    object_text = match.group(2).strip().rstrip('.')

                    source_concept = self._find_best_concept_match(subject_text, concepts)
                    target_concept = self._find_best_concept_match(object_text, concepts)

                    if source_concept and target_concept and source_concept != target_concept:
                        relations.append(RelationEdge(
                            source_concept=source_concept.concept_text,
                            target_concept=target_concept.concept_text,
                            relation_type=rel_type,
                            confidence=0.7, # Base confidence for pattern match
                            evidence=[sentence]
                        ))
                        break # Move to next sentence after finding a relation
        return relations

    def _find_best_concept_match(self, text: str, concepts: Dict[str, CognitiveConceptNode]) -> Optional[CognitiveConceptNode]:
        """Find the best matching concept node for a piece of text."""
        text_lower = text.lower().strip()
        
        # Direct match is always best
        if text_lower in concepts:
            return concepts[text_lower]
        
        # Partial match with scoring using Jaccard similarity
        best_match = None
        best_score = 0.4  # Require at least 40% word overlap
        
        text_words = set(text_lower.split())
        
        for concept_text, node in concepts.items():
            concept_words = set(concept_text.lower().split())
            
            intersection = len(text_words.intersection(concept_words))
            union = len(text_words.union(concept_words))
            score = intersection / union if union > 0 else 0
            
            if score > best_score: # pragma: no cover
                best_score = score
                best_match = node
        
        return best_match
    
    def _induce_principles(self, graph: ReasoningGraph) -> List[ReasoningPrinciple]:
        """Induce abstract principles from recurring patterns in the relation graph."""
        # This is a simplified version of principle induction.
        # A full implementation would require more sophisticated graph algorithms.
        principles = []
        
        # Example: Induce a 'uses' principle
        uses_relations = [r for r in graph.relations if r.relation_type == 'uses']
        if len(uses_relations) > 2: # Need enough examples
            principle = ReasoningPrinciple(
                name="System-Uses-Technique",
                abstract_structure=("?system", "uses", "?technique"),
                instantiations=uses_relations,
                confidence=0.8
            )
            principles.append(principle)
            
        return principles

    # ========================================================================
    # PHASE 2: PLAN-BASED REASONED GENERATION (Runtime Process)
    # ========================================================================
    
    def create_reasoning_plan(self, prompt: str, graph: ReasoningGraph) -> List[Dict]:
        """Create a structured discourse plan to answer a prompt."""
        plan = []
        prompt = prompt.lower()
        
        # Simple plan generation based on prompt keywords
        if "explain" in prompt or "what is" in prompt:
            # Find the main concept in the prompt
            concepts_in_prompt = [c for c in graph.concepts.values() if c.concept_text in prompt]
            if concepts_in_prompt:
                main_concept = max(concepts_in_prompt, key=lambda c: c.importance_score)
                plan.append({'action': 'define', 'concept': main_concept.concept_text})
                plan.append({'action': 'relate', 'concept': main_concept.concept_text})
        elif "compare" in prompt or "difference between" in prompt:
            concepts_in_prompt = [c for c in graph.concepts.values() if c.concept_text in prompt]
            if len(concepts_in_prompt) >= 2:
                plan.append({'action': 'define', 'concept': concepts_in_prompt[0].concept_text})
                plan.append({'action': 'define', 'concept': concepts_in_prompt[1].concept_text})
                plan.append({'action': 'contrast', 'concepts': [c.concept_text for c in concepts_in_prompt]})
        else:
            # Default fallback plan
            concepts_in_prompt = [c for c in graph.concepts.values() if c.concept_text in prompt]
            if concepts_in_prompt:
                main_concept = max(concepts_in_prompt, key=lambda c: c.importance_score)
                plan.append({'action': 'introduce', 'concept': main_concept.concept_text})
                plan.append({'action': 'elaborate', 'concept': main_concept.concept_text})

        return plan if plan else [{'action': 'fallback', 'prompt': prompt}]

    def get_plan_step_context(self, step: Dict, graph: ReasoningGraph) -> Tuple[str, np.ndarray]:
        """Generate a seed text and context vector for a plan step."""
        concept_text = step.get('concept')
        if not concept_text or concept_text not in graph.concepts:
            return "Thus, ", np.zeros(self.embedding_dim)

        concept = graph.concepts[concept_text]
        seed_text = f"Regarding {concept.concept_text}, "
        
        # Create a context vector focused on the concept and its direct relations
        related_embeddings = [concept.embedding]
        for rel, related_concept in graph.get_related_concepts(concept_text):
            if related_concept:
                related_embeddings.append(related_concept.embedding)
        
        context_vector = np.mean(related_embeddings, axis=0)
        return seed_text, context_vector

    # ========================================================================
    # PHASE 3: SYNTHESIS FROM PRINCIPLES (On-Demand Reasoning)
    # ========================================================================
    
    def synthesize_from_principle(self, principle: ReasoningPrinciple, graph: ReasoningGraph) -> str:
        """Generate a novel synthesis by applying an abstract principle."""
        if len(principle.instantiations) < 2:
            return "The principle requires more examples to generate a novel synthesis."

        # Pick two different examples to combine
        inst1 = principle.instantiations[0]
        inst2 = principle.instantiations[1]

        subj1 = inst1.source_concept
        obj1 = inst1.target_concept
        subj2 = inst2.source_concept
        obj2 = inst2.target_concept

        synthesis = (
            f"By abstracting from the principle '{principle.name}', we can propose a novel synthesis. "
            f"Given that '{subj1}' {principle.abstract_structure[1]} '{obj1}', and "
            f"'{subj2}' also {principle.abstract_structure[1]} '{obj2}', we can hypothesize "
            f"a new system where '{subj1}' could be enhanced by incorporating techniques from '{obj2}', "
            f"creating a hybrid approach to problem-solving."
        )
        return synthesis


# ============================================================================
# ADVANCED SAMPLING STRATEGIES
# ============================================================================

class AdvancedSampling:
    """Advanced sampling strategies for high-quality text generation."""
    
    @staticmethod
    def nucleus_sampling(probs: np.ndarray, p: float = 0.9) -> np.ndarray:
        """Nucleus (top-p) sampling for more diverse generation."""
        sorted_indices = np.argsort(probs)[::-1]
        sorted_probs = probs[sorted_indices]
        
        # Find nucleus
        cumulative_probs = np.cumsum(sorted_probs)
        nucleus_size = np.searchsorted(cumulative_probs, p) + 1
        
        # Create filtered distribution
        filtered_probs = np.zeros_like(probs)
        filtered_probs[sorted_indices[:nucleus_size]] = sorted_probs[:nucleus_size]
        
        # Renormalize
        filtered_probs = filtered_probs / np.sum(filtered_probs)
        
        return filtered_probs
    
    @staticmethod
    def top_k_sampling(probs: np.ndarray, k: int = 50) -> np.ndarray:
        """Top-k sampling to limit choices to most probable tokens."""
        top_k_indices = np.argsort(probs)[-k:]
        filtered_probs = np.zeros_like(probs)
        filtered_probs[top_k_indices] = probs[top_k_indices]
        
        # Renormalize
        filtered_probs = filtered_probs / np.sum(filtered_probs)
        
        return filtered_probs
    
    @staticmethod
    def temperature_annealing(temperature: float, step: int, annealing_rate: float = 0.99) -> float:
        """Gradually reduce temperature for more focused generation."""
        return max(0.1, temperature * (annealing_rate ** step))

# ============================================================================
# MAIN ADVANCED MARKOV AI CLASS
# ============================================================================

class AdvancedMarkovAI:
    """
    Advanced Markov AI with state space system and attention mechanisms.
    
    Features:
    - Multi-level state representation
    - Custom attention mechanisms
    - Instruction tuning capabilities
    - Hierarchical memory system
    - Advanced sampling strategies
    """
    
    def __init__(self, n_gram: int = 4, embedding_dim: int = 128, 
                 num_attention_heads: int = 8, memory_capacity: int = 1000):
        """Initialize Advanced Markov AI system."""
        self.n_gram = n_gram
        self.embedding_dim = embedding_dim
        self.num_attention_heads = num_attention_heads
        
        # Core components
        self.transition_matrix = defaultdict(Counter)
        self.vocabulary = set()
        self.word_embeddings = {}
        self.knowledge_vectors = []
        self.knowledge_texts = []
        
        # Advanced components
        self.memory_bank = MemoryBank(memory_capacity, embedding_dim)
        self.attention_mechanism = MultiHeadAttention(embedding_dim, num_attention_heads)
        self.instruction_tuner = InstructionTuner(self)
        self.advanced_sampling = AdvancedSampling()
        
        # Discourse planning
        self.discourse_planner = DiscoursePlanner()
        
        # State space parameters
        self.state_history = []
        self.context_window = 20
        self.importance_decay = 0.95
        
        # Learnable parameters
        self.alpha = 0.7  # Context importance weight
        self.beta = 0.3   # Knowledge importance weight
        self.gamma = 0.2  # Context update rate
        self.temperature = 1.0
        
        # Generation state
        self.current_context = None
        self.generation_step = 0
        
        # Quality tracking
        self.generation_quality_scores = []
        
        print(f"🧠 Advanced Markov AI initialized:")
        print(f"   • {n_gram}-gram with {embedding_dim}D embeddings")
        print(f"   • {num_attention_heads} attention heads")
        print(f"   • Memory capacity: {memory_capacity}")

    def _filter_knowledge_for_section(self, section: str) -> list:
        """Return knowledge base entries relevant to the given section."""
        section_keywords = {
            "Introduction": ["represents", "is", "encompasses", "involves", "overview", "introduction"],
            "Core Concepts": ["uses", "algorithms", "techniques", "methods", "mechanism", "principle", "explain"],
            "Applications": ["enables", "achieves", "applications", "systems", "use", "example", "real-world", "practical"],
            "Conclusion": ["therefore", "thus", "future", "implications", "summary", "conclusion"]
        }
        keywords = section_keywords.get(section, [])
        filtered = []
        for entry in self.knowledge_texts:
            if any(kw in entry.lower() for kw in keywords):
                filtered.append(entry)
        # Fallback: if nothing found, use all
        return filtered if filtered else self.knowledge_texts

    def _section_goal_vector(self, section: str) -> np.ndarray:
        """Create a distinctive goal vector for each section type."""
        goal_words = {
            "Introduction": ["define", "overview", "context", "introduce"],
            "Core Concepts": ["explain", "mechanism", "how", "process", "technical"],
            "Applications": ["use", "example", "application", "real-world", "practical"],
            "Conclusion": ["summary", "future", "implications", "therefore"]
        }
        words = goal_words.get(section, [])
        embeddings = [self.word_embeddings.get(w, np.zeros(self.embedding_dim)) for w in words]
        if embeddings:
            return np.mean(embeddings, axis=0)
        return np.zeros(self.embedding_dim)

    def _detect_domain(self, prompt: str) -> str:
        """Detects the domain of the prompt for domain-aware transitions."""
        prompt_lower = prompt.lower()
        if any(w in prompt_lower for w in ["biology", "photosynthesis", "cell", "organism", "plant", "chlorophyll"]):
            return "biology"
        if any(w in prompt_lower for w in ["history", "revolution", "empire", "ancient", "war"]):
            return "history"
        if any(w in prompt_lower for w in ["cooking", "recipe", "food", "ingredient", "kitchen"]):
            return "cooking"
        if any(w in prompt_lower for w in ["ai", "machine learning", "neural", "algorithm", "data"]):
            return "ai"
        return "general"

    def _generate_section_transition(self, prev_section: str, current_section: str, prompt: str) -> str:
        """Generate a domain-aware transition phrase between sections."""
        if prev_section is None:
            return ""
        domain = self._detect_domain(prompt)
        transitions = {
            "ai": {
                ("Introduction", "Core Concepts"): "To understand these paradigm shifts, we must examine the core concepts. ",
                ("Core Concepts", "Applications"): "Building on these foundations, let's explore practical applications. ",
                ("Applications", "Conclusion"): "Ultimately, these developments lead us to important conclusions. ",
            },
            "biology": {
                ("Introduction", "Core Concepts"): "To understand these biological processes, we must examine the core concepts. ",
                ("Core Concepts", "Applications"): "Building on these foundations, let's explore biological applications. ",
                ("Applications", "Conclusion"): "Ultimately, these discoveries lead us to important conclusions. ",
            },
            "history": {
                ("Introduction", "Core Concepts"): "To understand these historical events, we must examine the core concepts. ",
                ("Core Concepts", "Applications"): "Building on these foundations, let's explore historical implications. ",
                ("Applications", "Conclusion"): "Ultimately, these events lead us to important conclusions. ",
            },
            "cooking": {
                ("Introduction", "Core Concepts"): "To understand these culinary techniques, we must examine the core concepts. ",
                ("Core Concepts", "Applications"): "Building on these foundations, let's explore practical recipes. ",
                ("Applications", "Conclusion"): "Ultimately, these methods lead us to delicious results. ",
            },
            "general": {
                ("Introduction", "Core Concepts"): "To understand these key ideas, we must examine the core concepts. ",
                ("Core Concepts", "Applications"): "Building on these foundations, let's explore practical applications. ",
                ("Applications", "Conclusion"): "Ultimately, these developments lead us to important conclusions. ",
            }
        }
        key = (prev_section, current_section)
        if key in transitions.get(domain, {}):
            return transitions[domain][key]
        # Fallbacks
        if current_section == "Conclusion":
            return "In conclusion, "
        if current_section == "Applications":
            if domain == "biology":
                return "Now, let's consider some biological applications. "
            if domain == "history":
                return "Now, let's consider some historical implications. "
            if domain == "cooking":
                return "Now, let's consider some recipes. "
            return "Now, let's consider some applications. "
        if current_section == "Core Concepts":
            if domain == "biology":
                return "Let's examine the core biological concepts. "
            if domain == "history":
                return "Let's examine the core historical concepts. "
            if domain == "cooking":
                return "Let's examine the core culinary concepts. "
            return "Let's examine the core concepts. "
        return ""

    def generate_text_with_discourse(self, prompt: str, max_length: int = 100, temperature: float = 1.0,
                                     use_nucleus: bool = True, p: float = 0.9, repetition_penalty: float = 1.15,
                                     penalty_window: int = 30, min_length: int = 10, eos_damping: float = 0.3) -> Tuple[str, List[dict]]:
        """
        Section-aware generation using DiscoursePlanner.
        Each section is generated with explicit section context and goal awareness.
        Enhanced: Section-specific knowledge filtering and goal vectors.
        Now with natural section transitions.
        """
        plan = self.discourse_planner.create_plan(prompt)
        generated_sections = []
        decision_log = []
        total_words = 0
        max_len_per_section = max_length // len(plan) if plan else max_length
        used_phrases = set()
        prev_section = None

        for section in plan:
            section_goal = f"{section} of {prompt}"
            section_instruction = f"Write the {section.lower()} for: {prompt}"
            section_tokens = []
            section_words = 0

            # Section-specific knowledge and goal vector
            section_knowledge = self._filter_knowledge_for_section(section)
            goal_vector = self._section_goal_vector(section)

            # Generate transition phrase
            transition_phrase = self._generate_section_transition(prev_section, section, prompt)

            # Use the section as instruction for biasing generation
            # For the first section, if Introduction, skip the generic opener
            if prev_section is None and section == "Introduction":
                tokens = []
            else:
                tokens = self.preprocess_text(transition_phrase)
            if len(tokens) < self.n_gram - 1:
                tokens.extend(['the', 'following', 'text'][:self.n_gram - 1 - len(tokens)])
            generated_tokens = tokens.copy()

            while section_words < max_len_per_section:
                current_state = tuple(generated_tokens[-(self.n_gram-1):])
                penalty_tokens = generated_tokens[-penalty_window:]

                # Section-aware context: combine current state, section goal, and section knowledge
                context_tokens = list(current_state) + self.preprocess_text(section_goal)
                # Add top tokens from section knowledge
                if section_knowledge:
                    knowledge_tokens = self.preprocess_text(' '.join(section_knowledge[:2]))
                    context_tokens += knowledge_tokens
                context_embeddings = np.array([self.word_embeddings.get(tok, np.zeros(self.embedding_dim)) for tok in context_tokens])
                if len(context_embeddings) == 0:
                    context_embeddings = np.zeros((1, self.embedding_dim))
                attended_embeddings, _ = self.apply_attention_to_context(context_embeddings)
                # Combine with goal vector for distinctive section bias
                self.current_context = np.mean(attended_embeddings, axis=0) + 0.5 * goal_vector

                next_word, decision_details = self.generate_next_word_advanced(
                    current_state, penalty_tokens, temperature, use_nucleus, p, repetition_penalty,
                    min_length=min_length, eos_damping=eos_damping, current_length=section_words
                )
                generated_tokens.append(next_word)
                decision_log.append({**decision_details, "section": section})

                section_words += 1
                total_words += 1

                # Anti-repetition: penalize if phrase already used in previous sections
                if len(generated_tokens) >= 4:
                    phrase = ' '.join(generated_tokens[-4:])
                    if phrase in used_phrases:
                        # Semantic-aware substitution for anti-repetition
                        alt_word = self._semantic_substitute(generated_tokens[-1], section)
                        generated_tokens[-1] = alt_word
                        decision_log[-1]['anti_repetition'] = True
                    else:
                        used_phrases.add(phrase)

                if next_word == '<EOS>' and section_words >= min_length:
                    break

            # Mark section as complete
            self.discourse_planner.mark_section_complete(section)
            section_text = ' '.join(generated_tokens)
            section_text = self._postprocess_text(section_text)
            generated_sections.append(section_text)

            if total_words >= max_length:
                break

            prev_section = section

        full_text = '\n\n'.join(generated_sections)
        return full_text, decision_log

    def _semantic_substitute(self, word: str, section: str) -> str:
        """Return a semantic-aware synonym or paraphrase for anti-repetition."""
        # Simple synonym sets for demonstration
        synonyms = {
            "additionally": ["furthermore", "moreover", "in addition", "also", "besides"],
            "thus": ["therefore", "consequently", "as a result", "hence"],
            "example": ["instance", "illustration", "case", "demonstration"],
            "important": ["significant", "notable", "crucial", "vital"],
            "conclusion": ["summary", "closing", "final thoughts", "wrap-up"],
            "biological": ["cellular", "organic", "life-related"],
            "historical": ["past", "preceding", "bygone"],
            "applications": ["uses", "implementations", "practical cases"],
        }
        # Pick a synonym if available, else append a numeric suffix
        base = word.lower()
        if base in synonyms:
            import random
            return random.choice(synonyms[base])
        # Fallback: add "also" or "further" for generic cases
        if section.lower() == "applications":
            return "further"
        if section.lower() == "core concepts":
            return "notably"
        return "also"
    
    def preprocess_text(self, text: str) -> List[str]:
        """Enhanced text preprocessing with EOS token integration."""
        # Convert to lowercase and normalize whitespace
        text = re.sub(r'\s+', ' ', text.lower().strip())
        
        try:
            # Try NLTK tokenization first
            tokens = word_tokenize(text)
        except:
            # Fallback to regex tokenization that preserves key punctuation
            tokens = re.findall(r"[\w']+|[.,!?;]", text)
        
        # Define punctuation to remove, keeping essential ones for syntax
        punctuations_to_remove = set(string.punctuation) - {'.', ',', '!', '?', ';'}

        # Enhanced filtering to preserve single-letter words and key punctuation
        tokens = [token for token in tokens
                  if token not in punctuations_to_remove
                  and not token.isdigit()]
        
        # Replace sentence-ending punctuation with EOS token
        eos_tokens = []
        for token in tokens:
            if token in ['.', '!', '?']:
                eos_tokens.append('<EOS>')
            else:
                eos_tokens.append(token)
        
        return eos_tokens
    
    def _postprocess_text(self, text: str) -> str:
        """Cleans up spacing around punctuation and handles EOS tokens."""
        # Replace EOS tokens (robustly) with periods for natural-looking text
        text = re.sub(r'(<EOS>|eos)', '.', text, flags=re.IGNORECASE)
        
        # Remove space before common punctuation
        text = re.sub(r'\s+([.,!?;:])', r'\1', text)
        # Ensure space after punctuation if it's followed by a word
        text = re.sub(r'([.,!?;:])(\w)', r'\1 \2', text)
        
        # Clean up multiple consecutive periods
        text = re.sub(r'\.{2,}', '.', text)
        
        return text.strip()

    def build_markov_chain(self, corpus: str):
        """Build enhanced n-gram Markov chain with EOS token support."""
        print("🔗 Building advanced Markov chain with EOS token support...")
        self.corpus = corpus  # Store corpus for later use
        
        tokens = self.preprocess_text(corpus)
        self.vocabulary.update(tokens)
        
        # Ensure EOS token is in vocabulary
        self.vocabulary.add('<EOS>')
        
        # Build hierarchical n-gram models
        for n in range(2, self.n_gram + 1):
            for i in range(len(tokens) - n):
                state = tuple(tokens[i:i + n - 1])
                next_word = tokens[i + n - 1]
                self.transition_matrix[state][next_word] += 1
        
        # Convert to probabilities with smoothing
        for state in self.transition_matrix:
            total = sum(self.transition_matrix[state].values())
            for word in self.transition_matrix[state]:
                # Add-one smoothing
                self.transition_matrix[state][word] = (self.transition_matrix[state][word] + 1) / (total + len(self.vocabulary))
        
        print(f"✅ Enhanced Markov chain built with EOS support:")
        print(f"   • States: {len(self.transition_matrix)}")
        print(f"   • Vocabulary: {len(self.vocabulary)} (including <EOS>)")
    
    def load_embeddings(self):
        """Load and generate enhanced word embeddings with EOS token support."""
        print("🎯 Loading enhanced embeddings with EOS token...")
        
        try:
            if not GENSIM_AVAILABLE:
                raise ImportError("Gensim not available")
            
            print("🔄 Attempting to load pre-trained embeddings...")
            model = api.load('glove-wiki-gigaword-100')
            
            embedding_count = 0
            for word in self.vocabulary:
                if word == '<EOS>':
                    # Create special embedding for EOS token
                    self.word_embeddings[word] = self._generate_eos_embedding()
                elif word in model:
                    # Extend to desired dimension if needed
                    embedding = model[word]
                    if len(embedding) < self.embedding_dim:
                        # Pad with contextual information
                        padding = self._generate_contextual_padding(word, self.embedding_dim - len(embedding))
                        embedding = np.concatenate([embedding, padding])
                    elif len(embedding) > self.embedding_dim:
                        # Truncate
                        embedding = embedding[:self.embedding_dim]
                    
                    self.word_embeddings[word] = embedding
                    embedding_count += 1
                else:
                    self.word_embeddings[word] = self._generate_enhanced_embedding(word)
            
            print(f"✅ Loaded {embedding_count} pre-trained embeddings")
            print(f"🎯 Generated {len(self.vocabulary) - embedding_count - 1} enhanced embeddings")
            print(f"🛑 Created special EOS token embedding")
            
        except Exception as e:
            print(f"⚠️ Pre-trained embeddings not available, generating enhanced embeddings...")
            
            for word in self.vocabulary:
                if word == '<EOS>':
                    self.word_embeddings[word] = self._generate_eos_embedding()
                else:
                    self.word_embeddings[word] = self._generate_enhanced_embedding(word)
            
            print(f"✅ Generated enhanced embeddings for {len(self.word_embeddings)} words")
            print(f"🛑 Created special EOS token embedding")
    
    def _generate_enhanced_embedding(self, word: str) -> np.ndarray:
        """Generate enhanced contextual embedding with semantic clustering."""
        embedding = np.random.normal(0, 0.1, self.embedding_dim)
        
        # Semantic clustering based on word patterns and meaning
        word_lower = word.lower()
        
        # Morphological features
        if word_lower.endswith('ing'):
            embedding[0:16] += 0.4  # Gerund/present participle cluster
        if word_lower.endswith('ed'):
            embedding[16:32] += 0.4  # Past tense cluster
        if word_lower.endswith('ly'):
            embedding[32:48] += 0.4  # Adverb cluster
        if word_lower.endswith(('tion', 'sion', 'ness', 'ment')):
            embedding[48:64] += 0.4  # Abstract noun cluster
        
        # Semantic domain clustering
        tech_terms = ['data', 'compute', 'algorithm', 'network', 'system', 'process']
        ai_terms = ['ai', 'machine', 'learn', 'neural', 'intelligence', 'model']
        
        if any(term in word_lower for term in tech_terms):
            embedding[64:80] += 0.5  # Technical domain
        if any(term in word_lower for term in ai_terms):
            embedding[80:96] += 0.5  # AI domain
        
        # Length and complexity features
        if len(word) > 8:
            embedding[96:112] += 0.3  # Complex word cluster
        if len(word) < 4:
            embedding[112:128] += 0.3  # Simple word cluster
        
        # Normalize to unit vector
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = embedding / norm
            
        return embedding
    
    def _generate_contextual_padding(self, word: str, padding_size: int) -> np.ndarray:
        """Generate contextual padding for embeddings."""
        padding = np.random.normal(0, 0.05, padding_size)
        
        # Add word-specific information to padding
        word_hash = hash(word) % 1000
        padding[0] = word_hash / 1000.0
        
        return padding
    
    def _generate_eos_embedding(self) -> np.ndarray:
        """Generate special embedding for the EOS token."""
        # Create a distinctive embedding for the end-of-sequence token
        embedding = np.zeros(self.embedding_dim)
        
        # Set specific pattern for EOS token recognition
        # Use a strong signal in the first quarter of the embedding
        embedding[:self.embedding_dim//4] = 0.8
        
        # Add some variation to make it learnable but distinctive
        embedding[self.embedding_dim//4:self.embedding_dim//2] = -0.3
        
        # Add semantic meaning: "completion", "end", "finality"
        embedding[self.embedding_dim//2:3*self.embedding_dim//4] = 0.5
        
        # Final quarter with termination signal
        embedding[3*self.embedding_dim//4:] = 0.2
        
        # Add small amount of noise for robustness
        noise = np.random.normal(0, 0.05, self.embedding_dim)
        embedding += noise
        
        # Normalize to maintain consistent magnitude
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = embedding / norm
            
        return embedding
    
    def compute_state_importance(self, tokens: List[str], embeddings: List[np.ndarray]) -> float:
        """Compute importance score for a state snapshot."""
        if not embeddings:
            return 0.0
        
        # Factors for importance:
        # 1. Semantic diversity
        if len(embeddings) > 1:
            similarities = []
            for i in range(len(embeddings)):
                for j in range(i + 1, len(embeddings)):
                    sim = cosine_similarity([embeddings[i]], [embeddings[j]])[0][0]
                    similarities.append(sim)
            diversity = 1.0 - np.mean(similarities) if similarities else 0.5
        else:
            diversity = 0.5
        
        # 2. Rare word bonus
        vocab_size = len(self.vocabulary)
        rare_word_bonus = sum(1 for token in tokens if token in self.vocabulary) / max(len(tokens), 1)
        
        # 3. Context relevance (simplified)
        context_relevance = 0.7  # Placeholder
        
        importance = (diversity * 0.4 + rare_word_bonus * 0.3 + context_relevance * 0.3)
        return np.clip(importance, 0.0, 1.0)
    
    def update_state_history(self, tokens: List[str], context_vector: np.ndarray):
        """Update state history with new generation step."""
        # Create embeddings for tokens
        embeddings = []
        for token in tokens:
            if token in self.word_embeddings:
                embeddings.append(self.word_embeddings[token])
        
        if not embeddings:
            return
        
        # Compute importance score
        importance = self.compute_state_importance(tokens, embeddings)
        
        # Create state snapshot
        snapshot = StateSnapshot(
            tokens=tokens.copy(),
            embeddings=embeddings.copy(),
            attention_weights=np.ones(len(embeddings)) / len(embeddings),  # Uniform initially
            context_vector=context_vector.copy(),
            timestamp=self.generation_step,
            importance_score=importance
        )
        
        # Add to memory bank
        self.memory_bank.add_memory(snapshot)
        
        # Update state history
        self.state_history.append(snapshot)
        if len(self.state_history) > self.context_window:
            self.state_history.pop(0)
    
    def apply_attention_to_context(self, current_embeddings: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Apply multi-head attention to current context."""
        if len(self.state_history) == 0:
            return current_embeddings, np.ones((1, 1))
        
        # Gather historical embeddings
        historical_embeddings = []
        for snapshot in self.state_history[-self.context_window:]:
            if snapshot.embeddings:
                # Average embeddings in each snapshot
                avg_embedding = np.mean(snapshot.embeddings, axis=0)
                historical_embeddings.append(avg_embedding)
        
        if not historical_embeddings:
            return current_embeddings, np.ones((1, 1))
        
        # Combine current and historical embeddings
        all_embeddings = np.vstack([historical_embeddings, current_embeddings])
        
        # Apply multi-head attention
        attended_embeddings, attention_weights = self.attention_mechanism.forward(all_embeddings)
        
        # Return attended current embeddings
        return attended_embeddings[-len(current_embeddings):], attention_weights
    
    def generate_next_word_advanced(self, current_state: tuple, recent_tokens: List[str],
                                  temperature: float = 1.0, use_nucleus: bool = True, p: float = 0.9,
                                  repetition_penalty: float = 1.0, min_length: int = 10,
                                  eos_damping: float = 0.3, current_length: int = 0) -> Tuple[str, Dict]:
        """Advanced next word generation with EOS damping and attention."""
        # Get base candidates
        candidates, base_probs = self._get_base_candidates(current_state)
        
        if not candidates:
            return self._fallback_generation(), {}
        
        # Apply EOS damping if current length is below minimum
        if current_length < min_length and '<EOS>' in candidates:
            eos_idx = candidates.index('<EOS>')
            original_eos_prob = base_probs[eos_idx]
            # Reduce EOS probability by damping factor
            base_probs[eos_idx] *= eos_damping
            # Renormalize to maintain probability distribution
            base_probs = base_probs / np.sum(base_probs)
            print(f"   🔧 EOS damping applied: {original_eos_prob:.4f} → {base_probs[eos_idx]:.4f} (length {current_length}/{min_length})")
        
        # Prepare current context
        current_tokens = list(current_state)
        current_embeddings = np.array([self.word_embeddings.get(token, np.zeros(self.embedding_dim))
                                     for token in current_tokens])
        
        if len(current_embeddings) == 0:
            current_embeddings = np.zeros((1, self.embedding_dim))
        
        # Apply attention to context
        attended_embeddings, attention_weights = self.apply_attention_to_context(current_embeddings)
        
        # Update context vector
        self.current_context = np.mean(attended_embeddings, axis=0)
        
        # Retrieve relevant memories
        relevant_memories = self.memory_bank.retrieve_relevant_memories(self.current_context, k=5)
        
        # Compute enhanced probabilities
        enhanced_probs = self._compute_enhanced_probabilities(
            candidates, base_probs, self.current_context, relevant_memories
        )
        
        # Apply repetition penalty
        if repetition_penalty != 1.0 and recent_tokens:
            recent_token_set = set(recent_tokens)
            for i, candidate in enumerate(candidates):
                if candidate in recent_token_set:
                    # Penalize by dividing the probability
                    enhanced_probs[i] /= repetition_penalty
            
            # Renormalize
            if np.sum(enhanced_probs) > 0:
                enhanced_probs /= np.sum(enhanced_probs)
            else: # All probabilities became zero, fallback
                return self._fallback_generation(), {}

        # Store pre-sampling probabilities for analysis
        pre_sampling_probs = enhanced_probs.copy()
        
        # Apply advanced sampling
        if use_nucleus:
            enhanced_probs = self.advanced_sampling.nucleus_sampling(enhanced_probs, p)
        
        # Apply temperature annealing
        annealed_temp = self.advanced_sampling.temperature_annealing(temperature, self.generation_step)
        final_probs = enhanced_probs.copy()  # Probabilities after nucleus sampling
        if annealed_temp != 1.0:
            final_probs = final_probs ** (1.0 / annealed_temp)
            final_probs = final_probs / np.sum(final_probs)
        
        # Sample next word
        # Ensure final_probs has the same shape as candidates
        if len(final_probs) != len(candidates):
            final_probs = np.zeros(len(candidates)) # Fallback
        chosen_idx = np.random.choice(len(candidates), p=final_probs)
        chosen_word = candidates[chosen_idx]
        
        # Update state history
        self.update_state_history(current_tokens + [chosen_word], self.current_context)
        self.generation_step += 1
        
        # Prepare decision details
        decision_details = {
            'candidates': candidates,
            'base_probs': base_probs.tolist(),
            'enhanced_probs': pre_sampling_probs.tolist(),
            'final_probs': final_probs.tolist(),
            'chosen_word': chosen_word,
            'attention_weights': attention_weights.tolist() if attention_weights is not None else [],
            'relevant_memories': len(relevant_memories),
            'temperature': annealed_temp,
            'eos_damping_applied': current_length < min_length and chosen_word == '<EOS>'
        }
        
        return chosen_word, decision_details
    
    def _get_base_candidates(self, current_state: tuple) -> Tuple[List[str], np.ndarray]:
        """Get base candidates and probabilities from Markov chain."""
        # Try different n-gram lengths
        for n in range(min(len(current_state) + 1, self.n_gram), 1, -1):
            state = current_state[-(n-1):] if n > 1 else ()
            
            if state in self.transition_matrix:
                candidates = list(self.transition_matrix[state].keys())
                probs = np.array([self.transition_matrix[state][word] for word in candidates])
                return candidates, probs
        
        # Fallback to most common words
        common_words = list(self.vocabulary)[:100]
        uniform_probs = np.ones(len(common_words)) / len(common_words)
        return common_words, uniform_probs
    
    def _compute_enhanced_probabilities(self, candidates: List[str], base_probs: np.ndarray,
                                      context_vector: np.ndarray, memories: List[StateSnapshot]) -> np.ndarray:
        """Compute enhanced probabilities using context and memory."""
        enhanced_probs = base_probs.copy()
        
        for i, word in enumerate(candidates):
            if word not in self.word_embeddings:
                continue
            
            word_embedding = self.word_embeddings[word]
            
            # Context similarity boost
            context_sim = cosine_similarity([word_embedding], [context_vector])[0][0]
            context_boost = self.alpha * max(0, context_sim)
            
            # Memory relevance boost
            memory_boost = 0
            if memories:
                memory_similarities = []
                for memory in memories:
                    mem_sim = cosine_similarity([word_embedding], [memory.context_vector])[0][0]
                    memory_similarities.append(max(0, mem_sim) * memory.importance_score)
                memory_boost = self.beta * np.mean(memory_similarities)
            
            # Apply boosts
            enhanced_probs[i] *= (1 + context_boost + memory_boost)
        
        # Normalize
        enhanced_probs = enhanced_probs / np.sum(enhanced_probs)
        return enhanced_probs
    
    def _fallback_generation(self) -> str:
        """Fallback word generation when no candidates available."""
        if self.vocabulary:
            return np.random.choice(list(self.vocabulary))
        return "the"
    
    def _get_continuation_word(self, current_state: tuple) -> str:
        """Get a suitable continuation word when EOS is overridden."""
        # Get candidates but exclude EOS token
        candidates, probs = self._get_base_candidates(current_state)
        
        # Remove EOS token from candidates
        if '<EOS>' in candidates:
            eos_idx = candidates.index('<EOS>')
            candidates = candidates[:eos_idx] + candidates[eos_idx+1:]
            probs = np.concatenate([probs[:eos_idx], probs[eos_idx+1:]])
        
        if len(candidates) > 0:
            # Renormalize probabilities
            probs = probs / np.sum(probs)
            # Choose from top candidates
            top_idx = np.argmax(probs)
            return candidates[top_idx]
        else:
            # Fallback to common continuation words
            continuation_words = ['and', 'that', 'with', 'in', 'for', 'to', 'of', 'the']
            return np.random.choice(continuation_words)
    
    def generate_text_advanced(self, seed_text: str, max_length: int = 100,
                             temperature: float = 1.0, use_nucleus: bool = True,
                             p: float = 0.9, instruction: str = None,
                             repetition_penalty: float = 1.15, penalty_window: int = 30,
                             min_length: int = 10, eos_damping: float = 0.3) -> Tuple[str, List[Dict]]:
        """Advanced text generation with EOS damping and minimum length controls."""
        print(f"🎯 Advanced generation: {min_length}-{max_length} words from '{seed_text}'")
        print(f"🛑 EOS damping factor: {eos_damping:.2f} (lower = more hesitant to stop)")
        
        # Reset generation state
        self.generation_step = 0
        self.current_context = None
        
        # Process instruction if provided
        if instruction:
            full_seed = f"{instruction}\n\n{seed_text}"
            print(f"📝 Using instruction: {instruction}")
        else:
            full_seed = seed_text
        
        # Initialize
        tokens = self.preprocess_text(full_seed)
        if len(tokens) < self.n_gram - 1:
            # Pad with context-appropriate words
            tokens.extend(['the', 'following', 'text'][:self.n_gram - 1 - len(tokens)])
        
        generated_tokens = tokens.copy()
        decision_log = []
        
        # Dynamic generation loop with EOS damping and minimum length
        words_generated = 0
        while words_generated < max_length:
            current_state = tuple(generated_tokens[-(self.n_gram-1):])
            
            # Define the window for repetition penalty
            penalty_tokens = generated_tokens[-penalty_window:]
            
            next_word, decision_details = self.generate_next_word_advanced(
                current_state, penalty_tokens, temperature, use_nucleus, p, repetition_penalty,
                min_length=min_length, eos_damping=eos_damping, current_length=words_generated
            )
            
            generated_tokens.append(next_word)
            decision_log.append(decision_details)
            words_generated += 1
            
            # Check for EOS token - but only after minimum length
            if next_word == '<EOS>':
                if words_generated >= min_length:
                    print(f"🛑 Thoughtful stopping point reached with EOS token after {words_generated} words")
                    print(f"   (minimum length {min_length} satisfied)")
                    break
                else:
                    print(f"⚠️ EOS generated at {words_generated} words but minimum {min_length} not reached - continuing...")
                    # Replace the EOS token with a continuation word and keep going
                    generated_tokens[-1] = self._get_continuation_word(current_state)
                    decision_details['eos_overridden'] = True
            
            # Quality check and adjustment
            if words_generated > 0 and words_generated % 20 == 0:
                self._adaptive_parameter_adjustment(decision_log[-20:])
            
            if words_generated % 25 == 0:
                print(f"  Generated {words_generated}/{max_length} words...")
        
        # Check if we hit the maximum length without EOS
        if words_generated >= max_length:
            print(f"⚠️ Reached maximum length ({max_length}) without EOS token")
        
        generated_text = ' '.join(generated_tokens)
        generated_text = self._postprocess_text(generated_text)
        
        # Compute generation quality score
        quality_score = self._compute_generation_quality(generated_text, decision_log)
        self.generation_quality_scores.append(quality_score)
        
        print(f"✅ Advanced generation complete! Quality score: {quality_score:.3f}")
        print(f"📊 Words generated: {words_generated}, Natural ending: {next_word == '<EOS>' if 'next_word' in locals() else False}")
        return generated_text, decision_log
    
    def _adaptive_parameter_adjustment(self, recent_decisions: List[Dict]):
        """Adaptively adjust parameters based on recent generation quality."""
        # Analyze recent decisions for patterns
        avg_temp = np.mean([d.get('temperature', 1.0) for d in recent_decisions])
        
        # Adjust based on diversity and coherence
        if avg_temp > 1.2:  # Too diverse
            self.alpha = min(0.9, self.alpha + 0.05)
        elif avg_temp < 0.5:  # Too conservative
            self.beta = min(0.6, self.beta + 0.05)
    
    def _compute_generation_quality(self, text: str, decision_log: List[Dict]) -> float:
        """Compute overall quality score for generated text."""
        # Factors: coherence, diversity, fluency, instruction following
        
        # Coherence: consistency of context usage
        attention_consistency = np.mean([len(d.get('attention_weights', [])) > 0 
                                       for d in decision_log])
        
        # Diversity: entropy of choices
        all_probs = []
        for decision in decision_log:
            probs = decision.get('enhanced_probs', [])
            if probs:
                all_probs.extend(probs)
        
        diversity = -np.sum([p * np.log(p + 1e-10) for p in all_probs]) if all_probs else 0
        diversity = min(diversity / 10, 1.0)  # Normalize
        
        # Fluency: average probability of chosen words
        fluency = np.mean([max(d.get('enhanced_probs', [0])) for d in decision_log])
        
        # Overall quality
        quality = (attention_consistency * 0.3 + diversity * 0.3 + fluency * 0.4)
        return np.clip(quality, 0.0, 1.0)
    
    def build_knowledge_base(self, knowledge_texts: List[str]):
        """Build enhanced knowledge base with attention integration."""
        print("🧠 Building enhanced knowledge base...")
        
        self.knowledge_texts = knowledge_texts
        
        for text in knowledge_texts:
            tokens = self.preprocess_text(text)
            if tokens:
                embeddings = []
                for token in tokens:
                    if token in self.word_embeddings:
                        embeddings.append(self.word_embeddings[token])
                
                if embeddings:
                    # Apply attention to knowledge text
                    embeddings_array = np.array(embeddings)
                    attended_embeddings, _ = self.attention_mechanism.forward(embeddings_array)
                    
                    # Use attention-weighted average
                    avg_embedding = np.mean(attended_embeddings, axis=0)
                    self.knowledge_vectors.append(avg_embedding)
                else:
                    fallback_embedding = self._create_enhanced_text_embedding(text)
                    self.knowledge_vectors.append(fallback_embedding)
        
        self.knowledge_vectors = np.array(self.knowledge_vectors)
        print(f"✅ Enhanced knowledge base built with {len(self.knowledge_vectors)} entries")
    
    def _create_enhanced_text_embedding(self, text: str) -> np.ndarray:
        """Create enhanced embedding for text with domain awareness."""
        embedding = np.random.normal(0, 0.05, self.embedding_dim)
        
        text_lower = text.lower()
        
        # Enhanced domain detection
        domains = {
            'technical': ['algorithm', 'data', 'computer', 'system', 'process', 'method'],
            'ai_ml': ['artificial', 'intelligence', 'machine', 'learning', 'neural', 'model'],
            'scientific': ['research', 'study', 'analysis', 'experiment', 'theory'],
            'business': ['market', 'business', 'customer', 'product', 'service'],
            'creative': ['design', 'art', 'creative', 'innovation', 'idea']
        }
        
        domain_ranges = {
            'technical': (0, 25),
            'ai_ml': (25, 50),
            'scientific': (50, 75),
            'business': (75, 100),
            'creative': (100, 125)
        }
        
        for domain, terms in domains.items():
            if any(term in text_lower for term in terms):
                start, end = domain_ranges[domain]
                if end <= self.embedding_dim:
                    embedding[start:end] += 0.3
        
        # Normalize
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = embedding / norm
            
        return embedding
    
    def generate_text(self, *args, **kwargs) -> Tuple[str, List[Dict]]:
        """Compatibility method that calls generate_text_advanced."""
        return self.generate_text_advanced(*args, **kwargs)
    
    def explain_advanced_decision(self, decision_details: Dict, top_k: int = 5):
        """Provide detailed explanation of advanced decision process."""
        print(f"\n🔍 Advanced Decision Analysis")
        print("=" * 60)
        
        candidates = decision_details.get('candidates', [])
        base_probs = decision_details.get('base_probs', [])
        enhanced_probs = decision_details.get('enhanced_probs', [])
        final_probs = decision_details.get('final_probs', [])
        chosen_word = decision_details.get('chosen_word', '')
        attention_weights = decision_details.get('attention_weights', [])
        
        if not candidates:
            print("No decision details available.")
            return
        
        # Show top candidates
        sorting_probs = final_probs if final_probs else enhanced_probs
        sorted_indices = np.argsort(sorting_probs)[::-1][:top_k]
        chosen_in_top_k = False
        
        print(f"Top {top_k} candidates:")
        print("-" * 40)
        
        for i, idx in enumerate(sorted_indices):
            if idx < len(candidates):
                word = candidates[idx]
                base_prob = base_probs[idx] if idx < len(base_probs) else 0
                enhanced_prob = enhanced_probs[idx]
                final_prob = final_probs[idx] if final_probs else enhanced_prob
                boost = enhanced_prob / (base_prob + 1e-10)
                
                marker = " ← CHOSEN" if word == chosen_word else ""
                if word == chosen_word:
                    chosen_in_top_k = True
                print(f"{i+1}. '{word}'{marker}")
                print(f"   Base prob:     {base_prob:.4f}")
                print(f"   Enhanced prob: {enhanced_prob:.4f}")
                if final_probs:
                    print(f"   Final prob:    {final_prob:.4f}")
                print(f"   Boost factor:  {boost:.2f}x")
                print()
        
        # If chosen word was not in top_k, display it separately
        if not chosen_in_top_k and chosen_word in candidates:
            print("Chosen word (not in top_k):")
            print("-" * 40)
            idx = candidates.index(chosen_word)
            base_prob = base_probs[idx] if idx < len(base_probs) else 0
            enhanced_prob = enhanced_probs[idx] if idx < len(enhanced_probs) else 0
            final_prob = final_probs[idx] if final_probs and idx < len(final_probs) else enhanced_prob
            boost = enhanced_prob / (base_prob + 1e-10)
            print(f"'{chosen_word}' ← CHOSEN")
            print(f"   Base prob:     {base_prob:.4f}")
            print(f"   Enhanced prob: {enhanced_prob:.4f}")
            if final_probs:
                print(f"   Final prob:    {final_prob:.4f}")
            print(f"   Boost factor:  {boost:.2f}x\n")

        # Attention analysis
        if attention_weights:
            print(f"Attention Analysis:")
            print(f"  • Context attention weights shape: {np.array(attention_weights).shape}")
            print(f"  • Average attention: {np.mean(attention_weights):.4f}")
            attention_array = np.array(attention_weights)
            print(f"  • Attention entropy: {-np.sum(attention_array * np.log(attention_array + 1e-10)):.4f}")
        
        # Memory usage
        memory_count = decision_details.get('relevant_memories', 0)
        print(f"Memory Integration:")
        print(f"  • Relevant memories retrieved: {memory_count}")
        
        # Temperature info
        temp = decision_details.get('temperature', 1.0)
        print(f"Sampling Parameters:")
        print(f"  • Applied temperature: {temp:.3f}")

# ============================================================================
# ENHANCED ADVANCED MARKOV AI WITH CONTEXTUAL MEMORY ATTENTION
# ============================================================================

class EnhancedAdvancedMarkovAI(AdvancedMarkovAI):
    """Enhanced version with the new contextual memory attention."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Replace the basic attention with contextual memory attention
        self.contextual_attention = ContextualMemoryAttention(
            self.embedding_dim,
            local_window=64,
            global_memories=12
        )
        # Provide access to embeddings for anti-repetition mechanisms
        self.contextual_attention._parent_embeddings = self.word_embeddings
        print(f"🔥 Enhanced Markov AI with Contextual Memory Attention initialized!")
    
    def update_state_history(self, tokens: List[str], context_vector: np.ndarray):
        """Enhanced state history update with contextual memories."""
        # Create embeddings for tokens
        embeddings = []
        for token in tokens:
            if token in self.word_embeddings:
                embeddings.append(self.word_embeddings[token])
        
        if not embeddings:
            return
        
        # Create contextual memory
        markov_state = tuple(tokens[-(self.n_gram-1):]) if len(tokens) >= self.n_gram-1 else tuple(tokens)
        contextual_memory = self.contextual_attention.create_contextual_memory(
            tokens, embeddings, markov_state, self.generation_step
        )
        
        if contextual_memory:
            self.contextual_attention.contextual_memories.append(contextual_memory)
        
        # Also update the original state history for compatibility
        super().update_state_history(tokens, context_vector)
    
    def generate_next_word_advanced(self, current_state: tuple, recent_tokens: List[str],
                                  temperature: float = 1.0, use_nucleus: bool = True,
                                  p: float = 0.9, repetition_penalty: float = 1.0,
                                  min_length: int = 10, eos_damping: float = 0.3,
                                  current_length: int = 0) -> Tuple[str, Dict]:
        """Enhanced generation with contextual memory attention and EOS damping."""
        # Get base candidates from Markov chain
        candidates, base_probs = self._get_base_candidates(current_state)
        
        if not candidates:
            return self._fallback_generation(), {}
        
        # Apply EOS damping if current length is below minimum
        if current_length < min_length and '<EOS>' in candidates:
            eos_idx = candidates.index('<EOS>')
            original_eos_prob = base_probs[eos_idx]
            # Reduce EOS probability by damping factor
            base_probs[eos_idx] *= eos_damping
            # Renormalize to maintain probability distribution
            base_probs = base_probs / np.sum(base_probs)
            print(f"   🔧 Enhanced EOS damping applied: {original_eos_prob:.4f} → {base_probs[eos_idx]:.4f} (length {current_length}/{min_length})")
        
        # Prepare current context
        current_tokens = list(current_state)
        current_embeddings = np.array([
            self.word_embeddings.get(token, np.zeros(self.embedding_dim))
            for token in current_tokens
        ])
        
        if len(current_embeddings) == 0:
            current_embeddings = np.zeros((1, self.embedding_dim))
        
        # Compute current topic vector
        current_topic = self.contextual_attention._compute_topic_vector(
            current_tokens, [self.word_embeddings.get(token, np.zeros(self.embedding_dim))
                           for token in current_tokens]
        )
        
        # Retrieve relevant memories with anti-repetition
        current_context = np.mean(current_embeddings, axis=0)
        relevant_memories = self.contextual_attention.retrieve_relevant_memories(
            current_context, current_topic, current_tokens
        )
        
        # Apply contextual attention
        enhanced_context, attention_info = self.contextual_attention.compute_contextual_attention(
            current_embeddings, relevant_memories, base_probs
        )
        
        # Compute enhanced probabilities using the attention-enhanced context
        enhanced_probs = self._compute_enhanced_probabilities_with_attention(
            candidates, base_probs, enhanced_context, relevant_memories
        )
        
        # Apply repetition penalty
        if repetition_penalty != 1.0 and recent_tokens:
            recent_token_set = set(recent_tokens)
            for i, candidate in enumerate(candidates):
                if candidate in recent_token_set:
                    enhanced_probs[i] /= repetition_penalty
            
            if np.sum(enhanced_probs) > 0:
                enhanced_probs /= np.sum(enhanced_probs)
            else:
                return self._fallback_generation(), {}
        
        # Apply advanced sampling
        pre_sampling_probs = enhanced_probs.copy()
        if use_nucleus:
            enhanced_probs = self.advanced_sampling.nucleus_sampling(enhanced_probs, p)
        
        # Temperature annealing
        annealed_temp = self.advanced_sampling.temperature_annealing(temperature, self.generation_step)
        final_probs = enhanced_probs ** (1.0 / annealed_temp)
        final_probs = final_probs / np.sum(final_probs)
        
        # Sample next word
        chosen_idx = np.random.choice(len(candidates), p=final_probs)
        chosen_word = candidates[chosen_idx]
        
        # Update state history with enhanced method
        self.update_state_history(current_tokens + [chosen_word], enhanced_context.flatten())
        self.generation_step += 1
        
        # Enhanced decision details
        decision_details = {
            'candidates': candidates,
            'base_probs': base_probs.tolist(),
            'enhanced_probs': pre_sampling_probs.tolist(),
            'final_probs': final_probs.tolist(),
            'chosen_word': chosen_word,
            'attention_info': attention_info,
            'relevant_memories': len(relevant_memories),
            'temperature': annealed_temp,
            'contextual_enhancement': True,
            'eos_damping_applied': current_length < min_length and chosen_word == '<EOS>'
        }
        
        return chosen_word, decision_details
    
    def _compute_enhanced_probabilities_with_attention(self, candidates: List[str],
                                                     base_probs: np.ndarray,
                                                     enhanced_context: np.ndarray,
                                                     memories: List[ContextualMemory]) -> np.ndarray:
        """Compute probabilities using attention-enhanced context."""
        enhanced_probs = base_probs.copy()
        context_vector = enhanced_context.flatten() if len(enhanced_context.shape) > 1 else enhanced_context
        
        # Ensure context vector matches embedding dimension
        if context_vector.shape[0] != self.embedding_dim:
            if context_vector.shape[0] > self.embedding_dim:
                # Truncate to embedding dimension
                context_vector = context_vector[:self.embedding_dim]
            else:
                # Pad to embedding dimension
                padding = np.zeros(self.embedding_dim - context_vector.shape[0])
                context_vector = np.concatenate([context_vector, padding])
        
        for i, word in enumerate(candidates):
            if word not in self.word_embeddings:
                continue
            
            word_embedding = self.word_embeddings[word]
            
            # Context similarity boost (using enhanced context)
            context_sim = np.dot(word_embedding, context_vector) / (
                np.linalg.norm(word_embedding) * np.linalg.norm(context_vector) + 1e-8
            )
            context_boost = self.alpha * max(0, context_sim)
            
            # Memory relevance boost (using contextual memories)
            memory_boost = 0
            if memories:
                memory_similarities = []
                for memory in memories:
                    # Semantic similarity
                    sem_sim = np.dot(word_embedding, memory.semantic_summary) / (
                        np.linalg.norm(word_embedding) * np.linalg.norm(memory.semantic_summary) + 1e-8
                    )
                    # Weight by importance and recency
                    weighted_sim = sem_sim * memory.importance_score * memory.recency_weight
                    memory_similarities.append(max(0, weighted_sim))
                memory_boost = self.beta * np.mean(memory_similarities)
            
            # Anti-repetition penalty for word-level repetition
            repetition_penalty = 0.0
            if memories:
                recent_memory_tokens = []
                for memory in memories[-5:]:  # Last 5 memories
                    recent_memory_tokens.extend(memory.tokens)
                
                recent_word_count = recent_memory_tokens.count(word)
                if recent_word_count > 0:
                    # Exponential penalty for repeated words
                    repetition_penalty = min(0.7, recent_word_count * 0.15)
            
            # Apply boosts with anti-repetition
            total_boost = context_boost + memory_boost - repetition_penalty
            enhanced_probs[i] *= (1 + max(0, total_boost))
        
        # Normalize
        enhanced_probs = enhanced_probs / np.sum(enhanced_probs)
        return enhanced_probs
    
    def explain_enhanced_decision(self, decision_details: Dict, top_k: int = 5):
        """Enhanced decision explanation showing contextual memory attention details."""
        # First show the basic decision analysis
        self.explain_advanced_decision(decision_details, top_k)
        
        # Then add contextual memory attention details
        if decision_details.get('contextual_enhancement'):
            print(f"\n🧠 Contextual Memory Attention Analysis")
            print("=" * 50)
            
            attention_info = decision_details.get('attention_info', {})
            
            print(f"Local Attention:")
            print(f"  • Strength: {attention_info.get('local_attention_strength', 0):.4f}")
            
            print(f"Global Memory Attention:")
            print(f"  • Memories retrieved: {attention_info.get('global_memories_used', 0)}")
            print(f"  • Relevant memories used: {decision_details.get('relevant_memories', 0)}")
            
            print(f"Context Fusion:")
            print(f"  • Fusion strength: {attention_info.get('fusion_strength', 0):.4f}")
            
            print(f"Markov Integration:")
            print(f"  • Integration factor: {attention_info.get('markov_integration', 0):.4f}")
            
            # Memory usage statistics
            total_memories = len(self.contextual_attention.contextual_memories)
            print(f"Memory Bank Status:")
            print(f"  • Total contextual memories: {total_memories}")
            print(f"  • Memory utilization: {decision_details.get('relevant_memories', 0)}/{total_memories}")

# ============================================================================
# SAMPLE DATA AND KNOWLEDGE BASE
# ============================================================================

ENHANCED_CORPUS = """
Artificial intelligence represents a paradigm shift in computational thinking and problem-solving methodologies.
Machine learning algorithms demonstrate remarkable capabilities in pattern recognition and predictive analytics.
Deep learning architectures have revolutionized computer vision, natural language processing, and reinforcement learning.
Neural networks exhibit emergent behaviors that mirror aspects of biological cognition and information processing.
Natural language processing enables sophisticated human-computer interaction through semantic understanding.
Computer vision systems achieve superhuman performance in object detection, image classification, and scene analysis.
Reinforcement learning agents master complex sequential decision-making tasks through trial-and-error learning.
The transformer architecture has become the foundation for large language models and attention mechanisms.
Ethical AI considerations encompass fairness, transparency, accountability, and societal impact assessment.
Human-AI collaboration amplifies cognitive capabilities while preserving human creativity and judgment.
Explainable AI techniques make machine learning models more interpretable and trustworthy for critical applications.
Edge computing brings AI capabilities to resource-constrained devices and real-time processing scenarios.
Federated learning enables privacy-preserving machine learning across distributed data sources.
AutoML democratizes machine learning by automating model selection, hyperparameter tuning, and feature engineering.
Quantum machine learning explores the intersection of quantum computing and artificial intelligence algorithms.
AI safety research addresses alignment problems, robustness, and long-term existential risk considerations.
Multimodal AI systems integrate visual, textual, and auditory information for comprehensive understanding.
Generative AI creates novel content including text, images, code, and synthetic data for training purposes.
AI governance frameworks establish policies, regulations, and standards for responsible AI development.
Continual learning systems adapt to new information while retaining previously acquired knowledge.
"""

ENHANCED_KNOWLEDGE_BASE = [
    "Artificial intelligence encompasses machine learning, deep learning, natural language processing, and computer vision technologies.",
    "Machine learning algorithms learn patterns from data to make predictions or decisions without explicit programming.",
    "Deep learning uses multi-layered neural networks to model complex patterns and representations in data.",
    "Natural language processing involves computational techniques for understanding, generating, and manipulating human language.",
    "Computer vision enables machines to interpret and understand visual information from images and videos.",
    "Reinforcement learning trains agents to make sequential decisions through interaction with environments.",
    "Neural networks are computational models inspired by biological neural networks in the human brain.",
    "The transformer architecture uses self-attention mechanisms to process sequential data efficiently.",
    "Attention mechanisms allow models to focus on relevant parts of input when making predictions.",
    "Transfer learning leverages pre-trained models to solve related tasks with limited data.",
    "Unsupervised learning discovers hidden patterns in data without labeled examples.",
    "Supervised learning uses labeled training data to learn mappings from inputs to outputs.",
    "Feature engineering involves selecting and transforming variables to improve model performance.",
    "Cross-validation techniques assess model performance and prevent overfitting to training data.",
    "Hyperparameter tuning optimizes model configuration settings for better performance.",
    "Ensemble methods combine multiple models to achieve better predictive performance.",
    "Gradient descent optimization algorithms iteratively update model parameters to minimize loss functions.",
    "Regularization techniques prevent overfitting by adding constraints to model complexity.",
    "Evaluation metrics measure model performance across different aspects like accuracy, precision, and recall.",
    "AI ethics addresses bias, fairness, privacy, transparency, and accountability in artificial intelligence systems."
]

# ============================================================================
# INSTRUCTION TUNING EXAMPLES
# ============================================================================

INSTRUCTION_EXAMPLES = [
    {
        "instruction": "Explain the concept of machine learning in simple terms",
        "input": "What is machine learning?",
        "output": "Machine learning is a type of artificial intelligence where computers learn to make predictions or decisions by finding patterns in data, rather than being explicitly programmed for each task."
    },
    {
        "instruction": "Describe the benefits of deep learning",
        "input": "Why is deep learning important?",
        "output": "Deep learning is important because it can automatically discover complex patterns in large amounts of data, enabling breakthroughs in image recognition, natural language understanding, and many other AI applications."
    },
    {
        "instruction": "Compare supervised and unsupervised learning",
        "input": "What's the difference between supervised and unsupervised learning?",
        "output": "Supervise learning uses labeled training data to learn mappings from inputs to outputs, while unsupervised learning discovers hidden patterns in data without any labels or target outcomes."
    }
]
# ============================================================================
# REASONING MARKOV AI with CCRC - THE SUBJECT MATTER EXPERT
# ============================================================================

class ReasoningMarkovAI(EnhancedAdvancedMarkovAI):
    """
    The ultimate evolution: MarkovAI enhanced with the Cognitive-Causal Reasoning Core.
    This system can truly reason within its domain, moving beyond statistical generation
    to genuine subject matter expertise.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Initialize the Cognitive-Causal Reasoning Core
        self.reasoning_core = CognitiveCausalReasoningCore(self.embedding_dim)
        self.reasoning_graph = None
        
        print("💡 ReasoningMarkovAI with CCRC initialized!")
        print("🧠 Ready for deep reasoning and plan-based generation...")
    
    def build_reasoning_graph(self, corpus: str = None, knowledge_base: List[str] = None):
        """
        Build the reasoning graph from corpus and knowledge base.
        This is the core build-time process that creates the AI's "mental model".
        """
        if corpus is None:
            # Use the corpus that was used to build the Markov chain if available
            corpus = self.corpus if hasattr(self, 'corpus') and self.corpus else ""
        
        if knowledge_base is None:
            knowledge_base = self.knowledge_texts if hasattr(self, 'knowledge_texts') else [] # pragma: no cover
        
        # Phase 1: Build the reasoning graph
        self.reasoning_graph = self.reasoning_core.build_reasoning_graph(
            corpus, knowledge_base, self.word_embeddings
        )
        
        print("✅ Reasoning Graph ready for plan-based generation!")
        return self.reasoning_graph

    def generate_reasoned_text(self, seed_text: str, max_length: int = 100,
                                   temperature: float = 1.0, use_nucleus: bool = True,
                                   p: float = 0.9, instruction: str = None,
                                   repetition_penalty: float = 1.15, penalty_window: int = 30,
                                   min_length: int = 10, eos_damping: float = 0.3) -> Tuple[str, List[Dict]]:
        """
        Generate text using the CCRC's plan-based reasoning approach.
        """
        if not self.reasoning_graph:
            print("⚠️ Reasoning graph not built. Falling back to advanced generation.")
            return self.generate_text_advanced(seed_text, max_length, temperature, use_nucleus, p, instruction, repetition_penalty, penalty_window, min_length, eos_damping)

        print(f"🧠 CCRC-Enhanced Generation: {min_length}-{max_length} words")
        
        # Reset generation state
        self.generation_step = 0
        self.current_context = None
        
        # Phase 1: Create a reasoning plan
        prompt = instruction if instruction else seed_text
        plan = self.reasoning_core.create_reasoning_plan(prompt, self.reasoning_graph)
        print(f"📝 Generated Reasoning Plan with {len(plan)} steps.")

        # Phase 2: Execute the plan
        full_generated_text = self.preprocess_text(seed_text)
        decision_log = []
        max_len_per_step = max_length // len(plan) if plan else max_length

        for i, step in enumerate(plan):
            print(f"  Executing Plan Step {i+1}/{len(plan)}: {step['action']}...")
            
            # Generate text for the current plan step
            step_seed, step_context = self.reasoning_core.get_plan_step_context(step, self.reasoning_graph)
            
            # Use the parent's generator, but with strong contextual guidance from the plan
            step_text, step_log = super().generate_text_advanced(
                seed_text=' '.join(full_generated_text[-self.n_gram:]), # Use recent context
                max_length=max_len_per_step,
                instruction=step_seed, # The plan step guides the generation
                min_length=5
            )
            
            # Combine results
            step_tokens = self.preprocess_text(step_text)
            full_generated_text.extend(step_tokens[len(self.preprocess_text(' '.join(full_generated_text[-self.n_gram:]))):])
            
            # Add plan info to the log
            if step_log:
                step_log[0]['plan_step'] = step
                decision_log.extend(step_log)

            if len(full_generated_text) >= max_length:
                break

        generated_text = ' '.join(full_generated_text)
        generated_text = self._postprocess_text(generated_text)
        
        # Compute enhanced quality score
        quality_score = self._compute_reasoning_quality(generated_text, decision_log)
        self.generation_quality_scores.append(quality_score)
        
        print(f"✅ CCRC Generation complete! Quality score: {quality_score:.3f}")
        print(f"🧠 Reasoning plan fully executed.")
        
        return generated_text, decision_log
    
    def _compute_reasoning_quality(self, text: str, decision_log: List[Dict]) -> float:
        """Compute quality score including CCRC reasoning metrics."""
        # Base quality from parent class
        base_quality = self._compute_generation_quality(text, decision_log)
        
        # CCRC-specific quality factors: plan adherence
        plan_adherence = sum(1 for d in decision_log if 'plan_step' in d) / len(decision_log) if decision_log else 0
        
        # Bonus for plan-driven generation
        reasoning_bonus = 0.2 * plan_adherence
        
        return min(1.0, base_quality + reasoning_bonus)
    
    def explain_reasoning_decision(self, decision_details: Dict, top_k: int = 5):
        """Explain CCRC-enhanced decision with reasoning plan details."""
        # First show enhanced decision analysis
        self.explain_advanced_decision(decision_details, top_k)
        
        # Then add CCRC reasoning details
        if 'plan_step' in decision_details:
            print(f"\n🧠 Cognitive-Causal Reasoning Core Analysis")
            print("=" * 50)
            
            plan_step = decision_details['plan_step']
            print(f"Executing Plan Step: {plan_step}")
            
            print(f"Reasoning Graph Status:")
            if self.reasoning_graph:
                print(f"  • Concepts: {len(self.reasoning_graph.concepts)}")
                print(f"  • Relations: {len(self.reasoning_graph.relations)}")
                print(f"  • Principles: {len(self.reasoning_graph.principles)}")
            else:
                print(f"  • Graph not built yet")
    
    def generate_synthesis_from_principles(self, prompt: str = "Propose a novel concept") -> str:
        """Generate a novel conceptual synthesis by applying an abstract principle."""
        if not self.reasoning_graph or not self.reasoning_graph.principles:
            return "No reasoning principles available for synthesis. Build the reasoning graph first."
        
        print("💡 Generating Novel Synthesis from Abstract Principles...")
        
        # Select best principle for synthesis
        best_principle = max(self.reasoning_graph.principles, key=lambda p: p.confidence * len(p.instantiations))
        
        # Generate synthesis by applying the principle
        synthesis = self.reasoning_core.synthesize_from_principle(best_principle, self.reasoning_graph)
        
        print(f"🌟 Novel Synthesis Generated from principle '{best_principle.name}'")
        
        return synthesis
