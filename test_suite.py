#!/usr/bin/env python3
"""
Formal Test Suite for the Reasoning MarkovAI with its Cognitive-Causal Reasoning Core (CCRC).

This suite uses Python's `unittest` framework to provide structured testing for:
1.  Unit Tests: Verifying individual components in isolation.
2.  Integration Tests: Ensuring all parts of the CLW system work together.
3.  Regression Tests: Using snapshots to detect unexpected changes in output quality.

To run the tests, execute this file from your terminal:
python test_suite.py
"""

import unittest
import numpy as np
from markovai import ReasoningMarkovAI, ENHANCED_CORPUS, ENHANCED_KNOWLEDGE_BASE, CognitiveConceptNode

class TestCCRCComponents(unittest.TestCase):
    """Unit tests for the individual data structures and helper methods of the CCRC."""

    def test_concept_node_creation(self):
        """Tests that CognitiveConceptNode initializes correctly."""
        node = CognitiveConceptNode(
            concept_text="test concept",
            embedding=np.zeros(64),
            frequency=1,
            sentences=["This is a test."],
            importance_score=0.5
        )
        self.assertEqual(node.concept_text, "test concept")
        self.assertIsNotNone(node.properties)
        self.assertIsNotNone(node.states)

    def test_concept_extraction_cleanup(self):
        """Tests that the concept extraction filters out grammatically poor candidates."""
        # This test relies on the internal _extract_cognitive_concepts method.
        # In a real-world scenario, you might make this a public helper or test via the public API.
        ai = ReasoningMarkovAI(n_gram=3, embedding_dim=64)
        ai.load_embeddings() # Needed for concept embedding creation
        
        # A corpus with good and bad potential concepts
        test_corpus = "A good concept is machine learning. A bad one is encompasses machine learning. Another is for the."
        
        concept_nodes = ai.reasoning_core._extract_cognitive_concepts(test_corpus, ai.word_embeddings)
        
        extracted_concepts = [c.concept_text for c in concept_nodes]
        
        self.assertIn("machine learning", extracted_concepts)
        self.assertNotIn("encompasses machine learning", extracted_concepts, "Failed to filter out concept starting with a verb.")
        self.assertNotIn("for the", extracted_concepts, "Failed to filter out concept made of stopwords.")


class TestCCRCIntegration(unittest.TestCase):
    """Integration tests for the complete CCRC-Enhanced AI pipeline."""

    @classmethod
    def setUpClass(cls):
        """Set up the AI instance once for all tests in this class to save time."""
        print("\n--- Setting up ReasoningMarkovAI for Integration Tests ---")
        cls.ai = ReasoningMarkovAI(n_gram=3, embedding_dim=64)
        # Use a small subset of the data for faster testing
        corpus_subset = " ".join(ENHANCED_CORPUS.split()[:200])
        kb_subset = ENHANCED_KNOWLEDGE_BASE[:5]
        
        cls.ai.build_markov_chain(corpus_subset)
        cls.ai.load_embeddings()
        cls.ai.build_knowledge_base(kb_subset)
        cls.ai.build_reasoning_graph(corpus_subset, kb_subset)
        print("--- Setup Complete ---")

    def test_reasoning_graph_builds_successfully(self):
        """Tests that the reasoning graph is built and populated."""
        self.assertIsNotNone(self.ai.reasoning_graph)
        self.assertGreater(len(self.ai.reasoning_graph.concepts), 0, "Graph should have concept nodes.")
        self.assertGreater(len(self.ai.reasoning_graph.relations), 0, "Graph should have inferred relations.")

    def test_reasoning_generation_produces_output(self):
        """Tests that reasoning-enabled generation returns a non-empty string."""
        text, log = self.ai.generate_reasoned_text(
            "artificial intelligence",
            max_length=15
        )
        self.assertIsInstance(text, str)
        self.assertGreater(len(text), 0)
        self.assertIsInstance(log, list)

    def test_reasoning_plan_is_used(self):
        """Verify that a reasoning plan is created and used during generation."""
        _, log = self.ai.generate_reasoned_text(
            "neural networks",
            max_length=10
        )
        
        plan_used = any('plan_step' in d for d in log)
        self.assertTrue(plan_used, "Reasoning plan was not used in any generation step.")

    def test_synthesis_produces_output(self):
        """Tests that the synthesis function returns a coherent string."""
        if not self.ai.reasoning_graph or not self.ai.reasoning_graph.principles:
            self.skipTest("No reasoning principles induced, cannot test synthesis.")
            
        synthesis = self.ai.generate_synthesis_from_principles()
        self.assertIsInstance(synthesis, str)
        self.assertIn("principle", synthesis.lower()) # Check for expected template structure


class TestQualitativeRegression(unittest.TestCase):
    """Snapshot tests to catch major regressions in output quality."""

    def test_reasoning_output_snapshot(self):
        """Compares generated output to a stored 'golden' snapshot."""
        # In a real project, you would store this golden snapshot in a separate file.
        golden_snapshot = "artificial intelligence is a field of computer science. it involves creating systems that can perform tasks that normally require human intelligence. this includes things like learning from data and making decisions."
        
        ai = ReasoningMarkovAI(n_gram=4, embedding_dim=128)
        ai.build_markov_chain(ENHANCED_CORPUS)
        ai.load_embeddings()
        ai.build_reasoning_graph(ENHANCED_CORPUS, ENHANCED_KNOWLEDGE_BASE)
        
        # Debug: Print Markov chain next word candidates for ("artificial", "intelligence")
        state = ("artificial", "intelligence")
        if state in ai.transition_matrix:
            print("\n--- Markov Chain Debug: Next words for state ('artificial', 'intelligence') ---")
            for word, prob in ai.transition_matrix[state].items():
                print(f"'{word}': {prob:.4f}")
            print("-------------------------------------------------------------\n")
        else:
            print("\n--- Markov Chain Debug: State ('artificial', 'intelligence') not found ---\n")
        
        # Use a fixed seed for reproducibility
        np.random.seed(42)
        generated_text, _ = ai.generate_reasoned_text("artificial intelligence", max_length=25)
        
        # Print outputs for debugging
        print("\n=== CCRC Snapshot Test Debug ===")
        print("Generated Output:\n", generated_text)
        print("\nGolden Snapshot:\n", golden_snapshot)
        print("==============================\n")
        
        # Simple similarity check for regression
        generated_words = set(generated_text.split())
        golden_words = set(golden_snapshot.split())
        overlap = len(generated_words.intersection(golden_words)) / len(golden_words)
        
        self.assertGreater(overlap, 0.5, f"Output has diverged significantly from the golden snapshot. Overlap: {overlap:.2f}")


if __name__ == '__main__':
    unittest.main(verbosity=2)