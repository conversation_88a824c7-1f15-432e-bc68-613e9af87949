#!/usr/bin/env python3
"""
Quick test script to verify CLW implementation works correctly
"""

def test_clw_basic_functionality():
    """Test basic CLW functionality"""
    print("🧪 Testing CLW Implementation...")
    
    try:
        from markovai import CLWMarkovAI, ENHANCED_CORPUS, ENHANCED_KNOWLEDGE_BASE
        print("✅ CLW imports successful")
        
        # Initialize CLW system
        ai = CLWMarkovAI(n_gram=3, embedding_dim=64)  # Smaller for testing
        print("✅ CLW initialization successful")
        
        # Build components
        ai.build_markov_chain(ENHANCED_CORPUS[:500])  # Subset for speed
        print("✅ Markov chain built")
        
        ai.load_embeddings()
        print("✅ Embeddings loaded")
        
        ai.build_knowledge_base(ENHANCED_KNOWLEDGE_BASE[:5])  # Subset for speed
        print("✅ Knowledge base built")
        
        # Build conceptual lattice
        lattice = ai.build_conceptual_lattice(ENHANCED_CORPUS[:500], ENHANCED_KNOWLEDGE_BASE[:5])
        print("✅ Conceptual lattice built")
        print(f"   Concepts: {len(lattice.concept_nodes)}")
        print(f"   Arguments: {len(lattice.argument_nodes)}")
        print(f"   Bridges: {len(ai.discovered_bridges)}")
        
        # Test reasoning generation
        ai.enable_reasoning_mode()
        print("✅ Reasoning mode enabled")
        
        text, log = ai.generate_text_with_reasoning(
            "artificial intelligence",
            max_length=15,
            enable_reasoning=True
        )
        print(f"✅ Reasoning generation successful: {text[:100]}...")
        
        # Test synthesis
        if ai.discovered_bridges:
            synthesis = ai.generate_synthesis()
            print(f"✅ Synthesis generation successful: {synthesis[:100]}...")
        else:
            print("⚠️ No bridges discovered (expected with small dataset)")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_clw_components():
    """Test individual CLW components"""
    print("\n🔧 Testing CLW Components...")
    
    try:
        from markovai import ConceptNode, ArgumentNode, ConceptualLattice, ConceptualLatticeWeaver
        import numpy as np
        
        # Test ConceptNode
        embedding = np.random.normal(0, 0.1, 64)
        concept = ConceptNode(
            concept_text="machine learning",
            embedding=embedding,
            frequency=5,
            sentences=["Machine learning is powerful."],
            importance_score=0.8
        )
        print("✅ ConceptNode creation successful")
        
        # Test ArgumentNode
        argument = ArgumentNode(
            premise_concepts=[concept],
            conclusion_concepts=[concept],
            argument_type="causal",
            premise_text="Machine learning enables",
            conclusion_text="automated decision making",
            confidence_score=0.7
        )
        print("✅ ArgumentNode creation successful")
        
        # Test ConceptualLattice
        lattice = ConceptualLattice()
        lattice.add_concept_node(concept)
        lattice.add_argument_node(argument)
        print("✅ ConceptualLattice operations successful")
        
        # Test ConceptualLatticeWeaver
        clw = ConceptualLatticeWeaver(embedding_dim=64)
        print("✅ ConceptualLatticeWeaver initialization successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Component test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 CLW Implementation Test Suite")
    print("=" * 50)
    
    # Test components
    component_test = test_clw_components()
    
    # Test full functionality
    full_test = test_clw_basic_functionality()
    
    print("\n📊 Test Results:")
    print(f"   Component Tests: {'✅ PASSED' if component_test else '❌ FAILED'}")
    print(f"   Functionality Tests: {'✅ PASSED' if full_test else '❌ FAILED'}")
    
    if component_test and full_test:
        print("\n🎉 CLW Implementation: ALL TESTS PASSED! ✅")
        print("🧠 The Conceptual Lattice Weaver is ready for reasoning!")
    else:
        print("\n⚠️ Some tests failed. Check the output above for details.")