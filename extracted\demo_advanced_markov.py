#!/usr/bin/env python3
"""
Demonstration script for Advanced Markov AI
Shows how to use the enhanced features for high-quality text generation
"""

from markovai import AdvancedMarkovAI, EnhancedAdvancedMarkovAI, ENHANCED_CORPUS, ENHANCED_KNOWLEDGE_BASE, INSTRUCTION_EXAMPLES
import numpy as np

def enhanced_demo():
    """Demonstration of Enhanced Advanced Markov AI with Contextual Memory Attention."""
    print("🚀 Enhanced Advanced Markov AI Demo")
    print("🧠 Featuring Contextual Memory Attention")
    print("=" * 60)
    
    # Initialize the enhanced system
    ai = EnhancedAdvancedMarkovAI(
        n_gram=4,
        embedding_dim=128,
        num_attention_heads=8
    )
    
    # Build the system
    print("🔧 Building enhanced system components...")
    ai.build_markov_chain(ENHANCED_CORPUS)
    ai.load_embeddings()
    ai.build_knowledge_base(ENHANCED_KNOWLEDGE_BASE)
    
    # Add instruction tuning examples
    for example in INSTRUCTION_EXAMPLES:
        ai.instruction_tuner.add_training_example(
            example["instruction"],
            example["input"],
            example["output"]
        )
    
    print("✅ Enhanced system ready!")
    
    # Enhanced demonstration generations
    demos = [
        {
            "seed": "artificial intelligence",
            "instruction": "Write a technical explanation of AI capabilities",
            "params": {"max_length": 40, "temperature": 0.7, "p": 0.9, "min_length": 15, "eos_damping": 0.3}
        },
        {
            "seed": "contextual memory attention",
            "instruction": "Explain how contextual memory works in AI systems",
            "params": {"max_length": 35, "temperature": 0.8, "p": 0.85, "min_length": 12, "eos_damping": 0.25}
        },
        {
            "seed": "neural networks learn",
            "instruction": "Describe the learning process in neural networks",
            "params": {"max_length": 30, "temperature": 0.6, "p": 0.95, "min_length": 10, "eos_damping": 0.35}
        }
    ]
    
    results = []
    
    for i, demo in enumerate(demos, 1):
        print(f"\n🎯 Enhanced Demo {i}: {demo['seed']}")
        print(f"📝 Instruction: {demo['instruction']}")
        print("-" * 50)
        
        # Generate text with enhanced system
        generated_text, decision_log = ai.generate_text_advanced(
            seed_text=demo['seed'],
            instruction=demo['instruction'],
            **demo['params']
        )
        
        print(f"Generated: {generated_text}")
        
        # Show enhanced decision analysis for first word
        if decision_log:
            print("\n🔍 Enhanced Decision Analysis (First Word):")
            ai.explain_enhanced_decision(decision_log[0], top_k=3)
        
        results.append({
            'demo': demo,
            'generated': generated_text,
            'quality_score': ai.generation_quality_scores[-1] if ai.generation_quality_scores else 0
        })
    
    # Enhanced summary
    print(f"\n📊 Enhanced Demo Summary")
    print("=" * 40)
    avg_quality = sum(r['quality_score'] for r in results) / len(results)
    print(f"Average Quality Score: {avg_quality:.3f}")
    print(f"Contextual Memories: {len(ai.contextual_attention.contextual_memories)}")
    print(f"Traditional Memory Usage: {len(ai.memory_bank.short_term)} short-term memories")
    print(f"Vocabulary: {len(ai.vocabulary)} words")
    
    return ai, results

def quick_demo():
    """Quick demonstration of Advanced Markov AI capabilities."""
    print("🚀 Advanced Markov AI Quick Demo")
    print("=" * 50)
    
    # Initialize the system
    ai = AdvancedMarkovAI(
        n_gram=4,
        embedding_dim=128,
        num_attention_heads=8
    )
    
    # Build the system
    print("🔧 Building system components...")
    ai.build_markov_chain(ENHANCED_CORPUS)
    ai.load_embeddings()
    ai.build_knowledge_base(ENHANCED_KNOWLEDGE_BASE)
    
    # Add instruction tuning examples
    for example in INSTRUCTION_EXAMPLES:
        ai.instruction_tuner.add_training_example(
            example["instruction"],
            example["input"],
            example["output"]
        )
    
    print("✅ System ready!")
    
    # Demonstration generations
    demos = [
        {
            "seed": "artificial intelligence",
            "instruction": "Write a technical explanation",
            "params": {"max_length": 30, "temperature": 0.7, "p": 0.9}
        },
        {
            "seed": "machine learning",
            "instruction": "Explain this for beginners",
            "params": {"max_length": 25, "temperature": 0.8, "p": 0.85}
        },
        {
            "seed": "neural networks",
            "instruction": "Describe the key concepts",
            "params": {"max_length": 35, "temperature": 0.6, "p": 0.95}
        }
    ]
    
    results = []
    
    for i, demo in enumerate(demos, 1):
        print(f"\n🎯 Demo {i}: {demo['seed']}")
        print(f"📝 Instruction: {demo['instruction']}")
        print("-" * 40)
        
        # Generate text
        generated_text, decision_log = ai.generate_text_advanced(
            seed_text=demo['seed'],
            instruction=demo['instruction'],
            **demo['params']
        )
        
        print(f"Generated: {generated_text}")
        
        # Show decision analysis for first word
        if decision_log:
            print("\n🔍 Decision Analysis (First Word):")
            ai.explain_advanced_decision(decision_log[0], top_k=3)
        
        results.append({
            'demo': demo,
            'generated': generated_text,
            'quality_score': ai.generation_quality_scores[-1] if ai.generation_quality_scores else 0
        })
    
    # Summary
    print(f"\n📊 Demo Summary")
    print("=" * 30)
    avg_quality = sum(r['quality_score'] for r in results) / len(results)
    print(f"Average Quality Score: {avg_quality:.3f}")
    print(f"Memory Usage: {len(ai.memory_bank.short_term)} short-term memories")
    print(f"Vocabulary: {len(ai.vocabulary)} words")
    
    return ai, results

def interactive_demo(ai):
    """Interactive demonstration allowing user input."""
    is_enhanced = isinstance(ai, EnhancedAdvancedMarkovAI)
    demo_type = "Enhanced Interactive" if is_enhanced else "Interactive"
    
    print(f"\n🎮 {demo_type} Demo Mode")
    print("=" * 40)
    print("Enter your own prompts and instructions!")
    if is_enhanced:
        print("🧠 Using Contextual Memory Attention!")
    print("(Type 'quit' to exit)")
    
    while True:
        try:
            print("\n" + "="*50)
            seed = input("Enter seed text: ").strip()
            if seed.lower() == 'quit':
                break
                
            instruction = input("Enter instruction (optional): ").strip()
            if not instruction:
                instruction = None
                
            try:
                max_length = int(input("Max length (default 30): ") or "30")
                min_length = int(input("Min length (default 10): ") or "10")
                eos_damping = float(input("EOS damping 0.1-1.0 (default 0.3, lower=more hesitant): ") or "0.3")
                temperature = float(input("Temperature 0.1-2.0 (default 0.8): ") or "0.8")
                p = float(input("Nucleus p 0.1-1.0 (default 0.9): ") or "0.9")
            except ValueError:
                print("Using default parameters...")
                max_length, min_length, eos_damping, temperature, p = 30, 10, 0.3, 0.8, 0.9
            
            print(f"\n🎯 Generating {min_length}-{max_length} words (EOS damping: {eos_damping:.2f})...")
            generated_text, decision_log = ai.generate_text_advanced(
                seed_text=seed,
                instruction=instruction,
                max_length=max_length,
                min_length=min_length,
                eos_damping=eos_damping,
                temperature=temperature,
                p=p
            )
            
            print(f"\n✨ Generated Text:")
            print(f"{generated_text}")
            
            if decision_log:
                try:
                    show_analysis = input("\nShow decision analysis? (y/n): ").strip().lower()
                    if show_analysis == 'y':
                        if is_enhanced:
                            ai.explain_enhanced_decision(decision_log[0])
                        else:
                            ai.explain_advanced_decision(decision_log[0])
                except EOFError:
                    print("Skipping analysis display")
        except EOFError:
            print("\nExiting interactive demo")
            break

def train_and_evaluate_enhanced_system():
    """Train and evaluate the Enhanced Advanced Markov AI system."""
    print("🚀 Training Enhanced Advanced Markov AI System")
    print("🧠 With Contextual Memory Attention")
    print("=" * 70)
    
    # Initialize enhanced system
    ai = EnhancedAdvancedMarkovAI(
        n_gram=4,
        embedding_dim=128,
        num_attention_heads=8,
        memory_capacity=1000
    )
    
    # Build core components
    ai.build_markov_chain(ENHANCED_CORPUS)
    ai.load_embeddings()
    ai.build_knowledge_base(ENHANCED_KNOWLEDGE_BASE)
    
    # Add instruction tuning examples
    for example in INSTRUCTION_EXAMPLES:
        ai.instruction_tuner.add_training_example(
            example["instruction"],
            example["input"],
            example["output"],
            quality_score=1.0
        )
    
    print("\n🧪 Enhanced System Evaluation")
    print("=" * 70)
    
    test_cases = [
        {
            "seed": "contextual memory attention enables",
            "instruction": "Explain how contextual memory enhances AI reasoning",
            "max_length": 45
        },
        {
            "seed": "artificial intelligence learns from",
            "instruction": "Describe how AI systems learn and adapt",
            "max_length": 40
        },
        {
            "seed": "neural networks process information",
            "instruction": "Explain information processing in neural networks",
            "max_length": 50
        }
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n🧪 Enhanced Test Case {i+1}: {case['seed']}")
        print("-" * 60)
        
        # Generate with instruction using enhanced system
        generated_text, decision_log = ai.generate_text_advanced(
            seed_text=case['seed'],
            max_length=case['max_length'],
            temperature=0.8,
            use_nucleus=True,
            p=0.9,
            instruction=case.get('instruction')
        )
        
        print(f"Generated: {generated_text[:200]}...")
        
        # Analyze enhanced decision process
        if decision_log:
            ai.explain_enhanced_decision(decision_log[0])
            
    return ai

def train_and_evaluate_system():
    """Train and evaluate the Advanced Markov AI system."""
    print("🚀 Training Advanced Markov AI System")
    print("=" * 60)
    
    # Initialize system
    ai = AdvancedMarkovAI(
        n_gram=4,
        embedding_dim=128,
        num_attention_heads=8,
        memory_capacity=1000
    )
    
    # Build core components
    ai.build_markov_chain(ENHANCED_CORPUS)
    ai.load_embeddings()
    ai.build_knowledge_base(ENHANCED_KNOWLEDGE_BASE)
    
    # Add instruction tuning examples
    for example in INSTRUCTION_EXAMPLES:
        ai.instruction_tuner.add_training_example(
            example["instruction"],
            example["input"],
            example["output"],
            quality_score=1.0
        )
    
    # Perform instruction tuning (optional, can be slow)
    # ai.instruction_tuner.optimize_parameters(learning_rate=0.01, epochs=5)
    
    print("\n🧪 Advanced System Evaluation")
    print("=" * 60)
    
    test_cases = [
        {
            "seed": "artificial intelligence",
            "instruction": "Write about the future of AI technology",
            "max_length": 50
        },
        {
            "seed": "machine learning algorithms",
            "instruction": "Explain how machine learning works",
            "max_length": 40
        },
        {
            "seed": "deep learning networks",
            "instruction": "Describe deep learning applications",
            "max_length": 45
        }
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n🧪 Test Case {i+1}: {case['seed']}")
        print("-" * 50)
        
        # Generate with instruction
        generated_text, decision_log = ai.generate_text_advanced(
            seed_text=case['seed'],
            max_length=case['max_length'],
            temperature=0.8,
            use_nucleus=True,
            p=0.9,
            instruction=case.get('instruction')
        )
        
        print(f"Generated: {generated_text[:200]}...")
        
        # Analyze decision process
        if decision_log:
            ai.explain_advanced_decision(decision_log[0])
            
    return ai

if __name__ == "__main__":
    print("🎉 ADVANCED MARKOV AI DEMO SCRIPT")
    print("🧠 NOW WITH CONTEXTUAL MEMORY ATTENTION!")
    print("=" * 80)
    
    # Set random seed for reproducibility
    np.random.seed(42)

    # You can choose which demo to run
    print("Select a mode:")
    print("1: Enhanced Demo (🧠 Contextual Memory Attention - RECOMMENDED)")
    print("2: Quick Demo (Original Advanced System)")
    print("3: Enhanced Train and Evaluate (Comprehensive)")
    print("4: Original Train and Evaluate")
    
    choice = input("Enter choice (1-4): ").strip()

    if choice == '3':
        ai_system = train_and_evaluate_enhanced_system()
    elif choice == '4':
        ai_system = train_and_evaluate_system()
    elif choice == '2':
        ai_system, _ = quick_demo()
    else:
        # Default to enhanced demo
        ai_system, _ = enhanced_demo()

    # Ask if user wants interactive mode
    print("\n" + "="*60)
    try:
        run_interactive = input("Run interactive demo? (y/n): ").strip().lower()
        if run_interactive == 'y':
            interactive_demo(ai_system)
    except EOFError:
        print("Skipping interactive demo (no input available)")
    
    print("\n🎉 Script complete! Thanks for trying Advanced Markov AI!")
    print("🧠 Enhanced with Contextual Memory Attention!")